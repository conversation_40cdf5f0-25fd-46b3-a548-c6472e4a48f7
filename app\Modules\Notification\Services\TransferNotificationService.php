<?php

namespace App\Modules\Notification\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Epp\Constants\EppErrorCodes;
use App\Modules\Notification\Constants\NotificationType;
use App\Modules\Transfer\Constants\TransferRequest;
use App\Util\Constant\TableTypes;

class TransferNotificationService extends NotificationService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $transferNotificationService = new self;

        return $transferNotificationService;
    }

    public function sendBulkDomainTransferPendingRequestNotif(array $domains): void
    {
        $notification = NotificationHandler::For(auth()->id());

        foreach ($domains as $domain) {
            $message = 'Your domain transfer request for "'.strtoupper($domain).'" is currently being processed. Please be patient as we work on your request.';
            $notification->addPayload('Domain Transfer Pending Request', $message, 'transfer.inbound', NotificationType::IMPORTANT);
        }

        $notification->store();
        app(AuthLogger::class)->info($this->fromWho('Domains on process: '.implode(',', $domains)));
    }

    public function sendTransferPendingApprovalNotif(string $domain, string $userId, string $email): void
    {
        $message = 'Your domain transfer request for "'.strtoupper($domain).'" is currently awaiting approval. Please await further notification regarding your request.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Transfer Pending Approval', $message, 'transfer.inbound', NotificationType::IMPORTANT);
        $notification->store()->notifySelf()->updatePages([TableTypes::INBOUND_TRANSFER, TableTypes::DOMAIN]);
        app(AuthLogger::class)->info($this->fromWho('transfer request for '.$domain.' is now on pending approval.', $email));
    }

    public function sendPollTransferPendingNotif(string $domain, string $userId): void
    {
        $message = 'You have a pending domain transfer for "'.strtoupper($domain).'". Please click to view the request and take the appropriate action.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Transfer Pending', $message, 'transfer.outbound', NotificationType::IMPORTANT);
        $notification->store()->notifySelf()->updatePages([TableTypes::OUTBOUND_TRANSFER, TableTypes::DOMAIN]);
        app(AuthLogger::class)->info($this->fromWho('Notification pending domain transfer for '.$domain.'.', 'Cron:'));
    }

    public function sendTransferUserCancelledNotif(string $domain, string $userId): void
    {
        $message = 'The domain transfer for "'.strtoupper($domain).'" has been cancelled. A refund has been initiated.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Transfer Canceled', $message, 'transfer.inbound', NotificationType::CRITICAL, ['transaction' => TransferRequest::CONFLICT]);
        $notification->store()->notifySelf()->updatePages([TableTypes::INBOUND_TRANSFER, TableTypes::DOMAIN]);
        app(AuthLogger::class)->info($this->fromWho('Notification domain transfer for '.$domain.' has been cancelled. A refund has been initiated.', 'Cron:'));
    }

    public function sendTransferPollCancelledNotif(string $domain, string $userId): void
    {
        $message = 'The domain transfer for "'.strtoupper($domain).'" has been cancelled.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Transfer Canceled', $message, 'transfer.inbound', NotificationType::CRITICAL, ['transaction' => TransferRequest::CONFLICT]);
        $notification->store()->notifySelf()->updatePages([TableTypes::OUTBOUND_TRANSFER, TableTypes::DOMAIN]);
        app(AuthLogger::class)->info($this->fromWho('Notification domain transfer for '.$domain.' has been cancelled.', 'Cron:'));
    }

    public function sendTransferPollClientHoldNotif(string $domain, string $userId): void
    {
        $message = 'The domain transfer for "'.strtoupper($domain).'" has been rejected due to clientHold status.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Transfer Rejected', $message, 'transfer.outbound', NotificationType::CRITICAL, ['transaction' => TransferRequest::OUTBOUND]);
        $notification->store()->notifySelf()->updatePages([TableTypes::OUTBOUND_TRANSFER, TableTypes::DOMAIN]);
        app(AuthLogger::class)->info($this->fromWho('Notification domain transfer for '.$domain.' has been rejected due to clientHold status.', 'Cron:'));
    }

    public function sendTransferPollApprovedNotif(string $domain, string $userId): void
    {
        $message = 'The domain transfer for "'.strtoupper($domain).'" has been approved.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Transfer Approved', $message, 'transfer.inbound', NotificationType::MEDIUM, ['transaction' => TransferRequest::PREVIOUS]);
        $notification->store()->notifySelf()->updatePages([TableTypes::INBOUND_TRANSFER, TableTypes::DOMAIN]);
        app(AuthLogger::class)->info($this->fromWho('Notification domain transfer for '.$domain.' has been approved.'));
    }

    public function sendTransferUserApprovedNotif(string $domain, string $userId, string $email): void
    {
        $message = 'You have approved the transfer request for the domain "'.strtoupper($domain).'".';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Transfer Approved', $message, 'transfer.outbound', NotificationType::MEDIUM, ['transaction' => TransferRequest::PREVIOUS]);
        $notification->store()->notifySelf()->updatePages([TableTypes::OUTBOUND_TRANSFER, TableTypes::DOMAIN]);
        app(AuthLogger::class)->info($this->fromWho('Notification domain transfer for '.$domain.' has been approved.', $email));
    }

    public function sendTransferServerApprovedNotif(string $domain, string $userId, string $registered_domain_status): void
    {
        $message = 'The domain transfer for "'.strtoupper($domain).'" has been automatically approved by the server.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Transfer Approved', $message, 'transfer.outbound', NotificationType::MEDIUM, ['transaction' => TransferRequest::PREVIOUS]);
        $notification->store()->notifySelf();

        if ($registered_domain_status == UserDomainStatus::OWNED) {
            $notification->updatePages([TableTypes::OUTBOUND_TRANSFER, TableTypes::DOMAIN]);
        } elseif ($registered_domain_status == UserDomainStatus::RESERVED) {
            $notification->updatePages([TableTypes::INBOUND_TRANSFER, TableTypes::DOMAIN]);
        }

        app(AuthLogger::class)->info($this->fromWho('Notification domain transfer for '.$domain.' has been automatically approved by the server.', 'Cron:'));
    }

    public function sendTransferPollRejectedNotif(string $domain, string $userId): void
    {
        $message = 'The domain transfer for "'.strtoupper($domain).'" has been rejected. A refund has been initiated.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Transfer Rejected', $message, 'transfer.inbound', NotificationType::CRITICAL, ['transaction' => TransferRequest::PREVIOUS]);
        $notification->store()->notifySelf()->updatePages([TableTypes::INBOUND_TRANSFER]);
        app(AuthLogger::class)->info($this->fromWho('Poll notification domain transfer for '.$domain.' has been rejected. A refund has been initiated.', 'Cron:'));
    }

    public function sendTransferSystemRejectedNotif(string $domain, string $userId): void
    {
        $message = 'The domain transfer for "'.strtoupper($domain).'" has been rejected. Unable to process transfer due to request deletion process.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Transfer Rejected', $message, 'transfer.inbound', NotificationType::CRITICAL, ['transaction' => TransferRequest::PREVIOUS]);
        $notification->store()->notifySelf()->updatePages([TableTypes::OUTBOUND_TRANSFER]);
        app(AuthLogger::class)->info($this->fromWho('System notification domain transfer for '.$domain.' has been rejected. Unable to process transfer due to request deletion process', 'Cron:'));
    }

    public function sendTransferExpiredRejectedNotif(string $domain, string $userId): void
    {
        $message = 'The domain transfer for "'.strtoupper($domain).'" has been rejected. Domain has expired beyond the transfer eligibility period (44-45 days).';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Transfer Rejected', $message, 'transfer.inbound', NotificationType::CRITICAL, ['transaction' => TransferRequest::PREVIOUS]);
        $notification->store()->notifySelf()->updatePages([TableTypes::OUTBOUND_TRANSFER]);
        app(AuthLogger::class)->info($this->fromWho('System notification domain transfer for '.$domain.' has been rejected due to domain expiry beyond transfer eligibility period', 'Cron:'));
    }

    public function sendTransferUserRejectedNotif(string $domain, string $userId, string $email): void
    {
        $message = 'You have rejected the transfer request for the domain "'.strtoupper($domain).'".';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Transfer Rejected', $message, 'transfer.outbound', NotificationType::CRITICAL, ['transaction' => TransferRequest::PREVIOUS]);
        $notification->store()->notifySelf()->updatePages([TableTypes::OUTBOUND_TRANSFER, TableTypes::DOMAIN]);
        app(AuthLogger::class)->info($this->fromWho('Notification domain transfer for '.$domain.' has been rejected.', $email));
    }

    public function sendTransferConflictNotif(string $domain, string $userId, string $email): void
    {
        $message = 'Your transfer request for the domain "'.strtoupper($domain).'" has failed. Click to view the error.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Transfer Conflict', $message, 'transfer.inbound', NotificationType::CRITICAL, ['transaction' => TransferRequest::CONFLICT]);
        $notification->store()->notifySelf()->updatePages([TableTypes::INBOUND_TRANSFER]);
        app(AuthLogger::class)->info($this->fromWho('unable to request a transfer for the domain '.$domain.' due to conflicts.', $email));
    }

    public function sendTransferAutoRefundNotif(string $domain, string $userId, string $email, string $errorCode): void
    {
        $message = 'The domain "'.strtoupper($domain).'" '.EppErrorCodes::ERROR_MESSSAGE[$errorCode].'. A refund has been initiated.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Transfer Auto-Refunded', $message, 'transfer.inbound', NotificationType::CRITICAL, ['transaction' => TransferRequest::PREVIOUS]);
        $notification->store()->notifySelf()->updatePages([TableTypes::INBOUND_TRANSFER]);
        app(AuthLogger::class)->info($this->fromWho('auto-refund for '.$domain.' has been initiated.', $email));
    }

    public function sendInvalidTransferAutoRefundNotif(string $domain, string $userId, string $email): void
    {
        $message = 'The transfer request for the domain "'.strtoupper($domain).'" has been unresolved for 30 days. A refund has been initiated.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Transfer Auto-Refunded', $message, 'transfer.inbound', NotificationType::CRITICAL, ['transaction' => TransferRequest::PREVIOUS]);
        $notification->store()->notifySelf()->updatePages([TableTypes::INBOUND_TRANSFER]);
        app(AuthLogger::class)->info($this->fromWho('auto-refund for '.$domain.' has been initiated.', $email));
    }

    public function sendTransferDisabledFeatureNotif(string $userId, string $email): void
    {
        $message = 'The transfer feature has been disabled.';
        $notification = NotificationHandler::For($userId)->addPayload('Transfer Disabled', $message, 'transfer.outbound', NotificationType::CRITICAL, ['transaction' => TransferRequest::PREVIOUS]);
        $notification->store()->notifySelf()->updatePages([TableTypes::OUTBOUND_TRANSFER, TableTypes::DOMAIN]);
        app(AuthLogger::class)->info($this->fromWho('Notification domain transfer feature has been disabled.', $email));
    }

    // public function sendTestNotification(string $userId): void
    // {
    //     $message = 'This is a test notification';
    //     $notification = NotificationHandler::For($userId)->addPayload('Test Notification', $message, 'transfer.inbound');
    //     $notification->store()->notifySelf();
    // }
}
