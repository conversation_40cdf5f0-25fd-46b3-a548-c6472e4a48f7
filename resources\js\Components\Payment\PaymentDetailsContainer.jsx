import { MdAppRegistration, MdAssignmentReturn } from "react-icons/md";
import _PaymentSummary from "../../Constant/_PaymentSummary";
import transferUtil from "@/Util/transferUtil";
import _DateTimeFormat from "@/Constant/_DateTimeFormat";

export default function PaymentDetailsContainer({ data }) {
    const convertNameFormat = (str) => {
        str = str.toLowerCase();
        const words = str.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1));
        return words.join(' ');
    }

    const getItemAmount = (item) => {
        return parseFloat(item.total_domain_amount ?? 0).toFixed(2);

    }

    return (
        <>
            <div className="flex items-center justify-between space-y-8 pt-8 text-gray-600 border-b border-gray-200 pb-2 mb-4 ">
            </div>
            <div className="flex flex-col">
            <h3 className="text-lg font-semibold mb-3 text-slate-700 dark:text-slate-200">Payment Details</h3>
                <div className="flex items-center justify-between">
                    <span className="font-medium text-slate-500 dark:text-slate-400">Transaction Date</span>
                    <span className="font-mono text-slate-700 dark:text-slate-300">{transferUtil.convertDateTime(data.created_at, _DateTimeFormat.STRING)} {new Date(data.created_at + 'Z').toLocaleTimeString()}</span>
                </div>
                <div className="flex items-center justify-between">
                    <span className="font-medium text-slate-500 dark:text-slate-400">Transaction ID</span>
                    <span className="font-mono text-slate-700 dark:text-slate-300">{data.transaction_id}</span>
                </div>
                <div className="flex items-center justify-between">
                    <span className="font-medium text-slate-500 dark:text-slate-400">Payment Method</span>
                    <span className="font-mono text-slate-700 dark:text-slate-300">{_PaymentSummary.SERVICE_TYPE_TEXT[data.source] ?? 'Unknown'}</span>
                </div>
                {
                    data.source == "BANK_TRANSFER"
                        ?
                            <>
                                <div className="flex items-center justify-between">
                                    <span className="font-medium text-slate-500 dark:text-slate-400">Reference Number</span>
                                    <span className="font-mono text-slate-700 dark:text-slate-300">{data.bankTransferData.referenceNumber}</span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="font-medium text-slate-500 dark:text-slate-400">Purpose</span>
                                    <span className="font-mono text-slate-700 dark:text-slate-300 uppercase">{data.bankTransferData.purpose}</span>
                                </div>
                            </>
                        :
                            null
                }
            
            </div>
        </>
    );
}
