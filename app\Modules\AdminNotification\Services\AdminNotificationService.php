<?php

namespace App\Modules\AdminNotification\Services;

use App\Modules\Guest\Constants\RequestType;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdminNotificationService
{
    public static function instance()
    {
        $adminNotificationService = new self;

        return $adminNotificationService;
    }
    // Static functions for different notifications

    private function sendNotification($field)
    {
        return DB::table('admin_notifications')->insert($field);
    }

    public function sendClientRequestNotif($notifData)
    {
        switch ($notifData['request_type']) {
            case RequestType::REGISTRATION_ACCESS:
                $title = 'Registration Access Request';
                $request_type = 'REGISTRATION ACCESS';
                break;
            case RequestType::NEW_IP:
                $title = 'New IP Request';
                $request_type = 'NEW IP REGISTRATION';
                break;
        }

        $this->sendNotification([
            'title' => $title,
            'message' => 'Client "' . $notifData['email'] . '" is requesting for "' . $request_type . '". Go to the Request page to take appropriate action.',
            'redirect_url' => 'request',
            'importance' => 'medium',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }

    public function sendClientRegistrationNotif($notifData)
    {
        $this->sendNotification([
            'title' => 'New Client Registered',
            'message' => 'Client "' . $notifData['email'] . '" was registered. Go to the Client page to take appropriate action.',
            'redirect_url' => 'client',
            'importance' => 'medium',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }

    public function sendInsufficientBalanceNotif($notifData)
    {
        $this->sendNotification([
            'title' => 'Insufficient Balance',
            'message' => 'You have insufficient balance on your ' . $notifData['registry'] . ' account. Click to settle the account.',
            'redirect_url' => 'epp.account',
            'importance' => 'critical',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }

    public function sendInvalidAccountCreditBalanceNotif($notifData)
    {
        $this->sendNotification([
            'title' => 'Invalid Account Credit Balance',
            'message' => 'Client "' . $notifData['email'] . '" tried to deposit $' . $notifData['amount'] . ' with previous balance of $' . $notifData['balance'] . ' to Account Credit. Go to the Client page to take appropriate action.',
            'redirect_url' => 'client',
            'importance' => 'critical',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }

    public function sendNoGenesisBlockFound($notifData)
    {
        $this->sendNotification([
            'title' => 'No Genesis Block Found',
            'message' => 'Client "' . $notifData['email'] . '" tried to deposit to Account Credit. Go to the Client page to take appropriate action.',
            'redirect_url' => 'client',
            'importance' => 'critical',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }

    public function sendNoSystemCreditFound($notifData)
    {
        $this->sendNotification([
            'title' => 'No System Credit Found',
            'message' => 'Client "' . $notifData['email'] . '" has no system credits given to deposit to Account Credit. Go to the Client page to take appropriate action.',
            'redirect_url' => 'client',
            'importance' => 'critical',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }


    public function sendDomainDeletionRequest($domainName)
    {
        $this->sendNotification([
            'title' => 'Client Domain Deletion',
            'message' => 'The client has requested the deletion of domain "'.$domainName.'". Please visit the Domain Deletion page to proceed with the appropriate action.',
            'redirect_url' => 'domain.delete-request.view',
            'importance' => 'critical',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }

    public function sendBankTransferMotification($data)
    {
        $this->sendNotification(
            [
                'title'        => $data['title'],
                'message'      => $data['message'],
                'redirect_url' => 'billing.wire.transfer',
                'importance'   => 'high',
                'created_at'   => Carbon::now(),
                'updated_at'   => Carbon::now(),
            ]
        );
    }

    public function sendDomainTransferPending($domainName)
    {
        $this->sendNotification([
            'title' => 'Domain Transfer Pending',
            'message' => 'The domain transfer for "'.$domainName.'" is currently pending. Please visit the Domain Transfer page to proceed with the appropriate action.',
            'redirect_url' => 'transfer.view',
            'importance' => 'medium',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }
}
