<?php

namespace App\Mail\Payment;

use App\Mail\Constants\Links;
use App\Mail\Constants\MailDetails;
use App\Modules\AdminNotification\Services\AdminNotificationService;
use App\Modules\BankTransfer\Constants\BankTransferStatus;
use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;

class BankTransferMail extends Mailable implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $email;

    private $name;

    private $userId;

    private $bankTransfer;

    private $type;

    private $subjectString;

    private $greeting;

    private $redirectUrl;

    private $supportUrl;

    private $phone;

    private $backOffMinutes = 5;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    public $uniqueFor = 120; // 2 minutes

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;

    /**
     * The maximum number of unhandled exceptions to allow before failing.
     *
     * @var int
     */
    public $maxExceptions = 5;

    /**
     * Create a new message instance.
     */
    public function __construct(array $data)
    {
        $this->email         = $data['email'];
        $this->name          = $data['name'];
        $this->userId        = $data['user_id'];
        $this->bankTransfer  = $data['bank_transfer'];
        $this->type          = $data['type'];
        $this->subjectString = "{$this->bankTransfer->reference_number} {$data['subject']}";
        $this->greeting      = 'Dear '.$this->name.',';
        $this->supportUrl    = config('app.url').Links::CONTACT_US_PAGE;
        $this->redirectUrl   = route('account.balance.index');
        $this->phone         = MailDetails::PHONE_NUMBER;

    $this->sendAdminNotification();
    }

    public function uniqueId(): int
    {
        return Carbon::now()->timestamp;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: new Address(config('mail.from.address'), config('mail.from.sd_name')),
            subject: $this->subjectString,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {

        $view = $this->getView();

        return new Content(
            markdown: $view,
            with: [
                'greeting'     => $this->greeting,
                'senderName'   => config('mail.from.sd_name'),
                'redirectUrl'  => $this->redirectUrl,
                'supportUrl'   => $this->supportUrl,
                'phoneNumber'  => $this->phone,
                'bankTransfer' => $this->bankTransfer,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception): void
    {
        app(AuthLogger::class)->error('BankTransferMail: '.$exception->getMessage());
    }

    private function getView()
    {
        switch ($this->type) {
            case BankTransferStatus::VERIFIED:
                return 'Mails.BankTransferMail.Verified';
            case BankTransferStatus::REJECTED:
                return 'Mails.BankTransferMail.Rejected';
            case BankTransferStatus::UNVERIFIED:
                return 'Mails.BankTransferMail.Unverified';
            case BankTransferStatus::PENDING:
            default:
                return 'Mails.BankTransferMail.Pending';
        }
    }

    private function sendAdminNotification()
    {
        switch ($this->type) 
        {
            case BankTransferStatus::VERIFIED:
                break; 
            case BankTransferStatus::REJECTED:
                break; 
            case BankTransferStatus::UNVERIFIED:
                break; 
            case BankTransferStatus::PENDING:
            default:
                AdminNotificationService::instance()
                    ->sendBankTransferMotification(
                        [
                            "title"   => "Bank Transfer Verification",
                            "message" => "Client {$this->email} is requesting for Verification of Bank Transfer {$this->bankTransfer->reference_number} to Add Credit to Account Balance",
                        ]
                    );        
        }
    }
}
