<?php

namespace App\Modules\UserDomainExport\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\UserDomainExport\Requests\UserDomainExportBatchDeleteFormRequest;
use App\Modules\UserDomainExport\Requests\UserDomainExportGenerateFormRequest;
use App\Modules\UserDomainExport\Requests\UserDomainExportListFormRequest;
use App\Modules\UserDomainExport\Services\UserDomainExportService;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class UserDomainExportController extends Controller
{
    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        // ...
    }

    /**
     * Show Index Page
     */
    public function index(UserDomainExportListFormRequest $request): Response
    {
        return Inertia::render(
            'UserDomainExport/UserDomainExportIndex',
            (new UserDomainExportService)->processListData(
                $request->only('showItems', 'search', 'sortColumn', 'isSortDesc'),
                Auth::user()->id
            )
        );
    }

    /**
     * Store File
     */
    public function generate(UserDomainExportGenerateFormRequest $request): void
    {
        (new UserDomainExportService)->generateFile(
            $request->only(
                'filterColumns',
                'filterSelectedItems',
                'filterStatus',
                'filterOrderBy',
                'filterName',
                'filterTld',
                'filterCategory',
                'filterNameserver',
            ),
            Auth::user()->id,
        );
    }

    /**
     * Download File
     */
    public function download(int $id): StreamedResponse
    {
        return (new UserDomainExportService)->downloadFile($id);
    }

    /**
     * Delete File
     */
    public function delete(int $id): void
    {
        (new UserDomainExportService)->deleteFile($id);
    }

    /**
     * Batch Delete
     */
    public function batchDelete(UserDomainExportBatchDeleteFormRequest $request): void
    {
        (new UserDomainExportService)->batchDelete(
            $request->only(
                'selectedItems'
            ),
            Auth::user()->id
        );
    }

    /**
     * Clear Files
     */
    public function clear(): void
    {
        (new UserDomainExportService)->clearItems(Auth::user()->id);
    }

    public function downloadBatch(Request $request)
    {
        $ids = $request->input('selectedItems', []);
        
        return (new UserDomainExportService)->downloadBatch($ids, Auth::id());
    }

    public function downloadAll()
    {
        return (new UserDomainExportService)->downloadAll(Auth::id());
    }
}
