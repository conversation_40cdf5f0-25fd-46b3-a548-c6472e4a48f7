@component('mail::message')

## Pending Payment Invoice

<table style="width: 100%; border-collapse: collapse;">
    <tbody>
        <tr>
            <td style="text-align: left; padding: 5px;">Reference #:</td>
            <td style="text-align: right; padding: 5px;">{{$mailData['bankTransferReferenceNumber']}}</td>
        </tr>
        <tr>
            <td style="text-align: left; padding: 5px;">Invoice #:</td>
            <td style="text-align: right; padding: 5px;">{{$mailData['transactionId']}}</td>
        </tr>
        <tr>
            <td style="text-align: left; padding: 5px;">Date Issued:</td>
            <td style="text-align: right; padding: 5px;">{{$mailData['dateIssued']}}</td>
        </tr>
        <tr>
            <td style="text-align: left; padding: 5px;">Due Date:</td>
            <td style="text-align: right; padding: 5px;">{{$mailData['dateDue']}}</td>
        </tr>
        <tr>
            <td style="text-align: left; padding: 5px;">Status:</td>
            <td style="text-align: right; padding: 5px;"><strong>PENDING</strong></td>
        </tr>
    </tbody>
</table>

<hr>

Please complete the bank transfer by the due date to avoid cancellation of your order.

You may view the complete payment summary on your account on <a href={{$mailData['paymentSummaryLink']}}>this page.</a>

## Order Summary 

@foreach ($mailData['orders'] as $order)

{{-- <table style="width: 100%; border-collapse: collapse;"">
    <tr>
        <td align="left"><strong>{{ $order['domain'] }}</strong></td>
        <td align="right">${{ $order['price'] }}</td>
    </tr>
    <tr>
        <td align="left">Transfer Fee</td>
        <td align="right">${{ $order['transferFee'] }}</td>
    </tr>
    <tr>
        <td align="left">ICANN Fee</td>
        <td align="right">${{ $order['icannFee'] }}</td>
    </tr>
    <tr>
        <td align="left">Sub Total</td>
        <td align="right">${{ $order['subTotal'] }}</td>
    </tr>
</table>

---   --}}

<table style="width: 100%; border-collapse: collapse;"">
    <tr>
        <td align = "left">{{ $order['domain'] }}</td>
        <td align = "right">${{ $order['subTotal'] }}</td>
    </tr>
</table>

@endforeach

---

<table 
    width="100%" 
    style="margin-bottom: 10px;"
>
    <tr>
        <td align="left">Total Amount</td>
        <td align="right"><strong>${{ $mailData['amountDue'] }}</strong></td>
    </tr>
</table>

## *Sincerely,*
## *StrangeDomains*

@endcomponent