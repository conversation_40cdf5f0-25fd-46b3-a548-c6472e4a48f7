//* PACKAGES
import { router } from "@inertiajs/react";

//* ICONS
import { IoArrowBack } from "react-icons/io5";

//* COMPONENTS
import AccountCenterLayout from "@/Layouts/AccountCenterLayout";
import AccountCreditSelectionAddFundsComponent from "@/Components/AccountCredit/AccountCreditSelectionAddFundsComponent";

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES

export default function AccountBalanceSelectPaymentMethod(
    {
        paymentMethods,
        minimumDepositAmount,
        stripePublicKey, 
        stripeSecretKey,
        stripeFeeObj = [],
    }
) 
{
    //! PACKAGE
    //...
    
    //! STATES
    //... 

    //! VARIABLES
    //...
    
    //! USE EFFECTS
    //...

    //! FUNCTIONS
    function handleGoBack()
    {
        router.replace(route('account.balance.index'));
    }

    return (
        <AccountCenterLayout>
            <div
                className="mx-auto container max-w-[750px] mt-20"
            >
                <div
                    className="flex flex-col gap-5"
                >
                    <div
                        className={`
                            flex flex-row gap-2 items-center cursor-pointer
                        `}
                        onClick={handleGoBack}
                    >
                        <IoArrowBack
                            className='h-6 w-6'
                        />
                        <span className="text-2xl font-semibold">Account Balance</span>
                    </div>
                    <hr />
                    <div>
                        Choose Payment Method:
                    </div>
                    <AccountCreditSelectionAddFundsComponent
                        paymentMethods={paymentMethods} 
                        minimumDepositAmount={minimumDepositAmount}
                        stripePublicKey={stripePublicKey}
                        stripeSecretKey={stripeSecretKey}
                        initialSetup={false}
                        stripeFeeObj={stripeFeeObj}
                    />
                </div>
            </div>
        </AccountCenterLayout >
    );
}
