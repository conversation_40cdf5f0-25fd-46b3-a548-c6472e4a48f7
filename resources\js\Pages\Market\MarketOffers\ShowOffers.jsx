import React, { useEffect, useState } from 'react'
import UserLayout from '@/Layouts/UserLayout'
import DataTable from 'react-data-table-component';
import OfferHistoryPopup from '../components/OfferHistoryPopup';
import { FaSearch } from "react-icons/fa";
import { PiCurrencyDollarDuotone } from "react-icons/pi";
import { FaBalanceScaleLeft } from "react-icons/fa";
import { ImCross } from "react-icons/im";
import ConfirmPopUp from '../components/ConfirmPopUp';
import CounterOfferPopup from '../components/CounterOfferPopup';
import { router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import { MdOutlineFilterAlt } from 'react-icons/md';
import Filter from '../components/Filter';
import { getEventValue } from '@/Util/TargetInputEvent';

export default function ShowOffers({myoffers}) {

    const [offers, setOffers] = useState([]);
    const [modal, showModal] = useState(false);
    const [status, setStatus] = useState(false);
    const [cmodal, showCModal] = useState(false);
    const [comodal, showCOModal] = useState(false);
    const limit = route().params.limit || 20;
    const [search, setSearch] = useState(route().params.search || '');
    const [domain, setDomain] = useState({name: '', created_at: new Date(), offer_price: 0});

    const statusColors = {
        waiting: "bg-gray-500 text-white",
        offer_closed: "bg-red-500 text-white",
        counter_offer: "bg-yellow-500 text-black",
        offer_accepted: "bg-green-500 text-white",
        offer_rejected: "bg-pink-500 text-white",
        user_counter_offer: "bg-orange-500 text-white",

        paid_hold_pending: "bg-blue-500 text-white",
        paid_order_pending: "bg-purple-500 text-white",
        paid_transfer_pending: "bg-teal-500 text-white",
        paid_transfer_requested: "bg-indigo-500 text-white",
        paid_transfer_completed: "bg-lime-500 text-black",
    };

    const columns = [
        {
            id: 'domain',
            name: 'Domain',
            left: "true",
            selector: row => row.domain_name,
            sortable: true,
        },
        {
            id: 'price',
            name: 'Initial Offer',
            left: "true",
            selector: row => row.offer_price,
            cell: row => `$${row.offer_price}`,
            sortable: true,
            width: '126px'
        },
        {
            id: 'status_change',
            name: 'Last Status Update',
            left: "true",
            selector: row => row.updated_at,
            cell: row => `${new Date(row.updated_at).toLocaleString()}`,
            sortable: true,
            width: '180px'
        },
        {
            id: 'status',
            name: 'Status',
            left: "true",
            selector: row => row.offer_status,
            cell: row => getStatus(row.offer_status),
            sortable: true,
        },
        {
            id: 'current',
            name: 'Buy Now Price',
            left: "true",
            selector: row => row.counter_offer_price,
            cell: row => `${row.counter_offer_price > 0 ? `$${row.counter_offer_price}` : 'NA'}`,
            sortable: true,
            width: '150px'
        },
        {
            id: 'actions',
            name: 'Actions',
            selector: row => getDetailButton(row),
            sortable: true,
        },
    ];

    const handlePopUp = (row, status) => {
        setDomain(row)

        if(status == 'pay' || status == 'close') {
            showCModal(true);
            setStatus(status)
        } else if(status == 'counter_offer') {
            showCOModal(true);
        } else showModal(true)
    }

    const handleLimitChange = (e) => {
        router.get(
            route('offers'),
            {
                ...route().params, // preserve existing query params
                limit: e.target.value,
                page: 1,
            },
            {
                preserveState: true,
                preserveScroll: true,
                replace: true
            }
        );
    };

    const getDetailButton = (row) => {
        return <div className='flex gap-1 font-bold'>
            <div className='has-tooltip'>
                <span className='tooltip rounded shadow-lg p-1 bg-gray-100 text-primary px-1 -mt-8'>View History</span>
                <button onClick={() => { handlePopUp(row) }} className='bg-primary rounded-md font-bold text-lg text-white p-1.5 flex items-center space-x-2'>
                    <FaSearch className=' font-bold' />
                </button>
            </div>
            {
                (row.offer_status == 'counter_offer') ? <div className='has-tooltip'>
                    <span className='tooltip rounded shadow-lg p-1 bg-gray-100 text-yellow-700 px-1 -mt-8'>Counter Offer</span>
                    <button onClick={() => { handlePopUp(row, 'counter_offer') }} className='bg-yellow-700 rounded-md font-bold text-lg text-white p-1.5 flex items-center space-x-2'>
                        <FaBalanceScaleLeft className=' font-bold' />
                    </button>
                </div> : <></>
            }
            {
                (row.offer_status == 'offer_accepted' || row.offer_status == 'counter_offer') ? <>
                    <div className='has-tooltip'>
                        <span className='tooltip rounded shadow-lg p-1 bg-gray-100 text-green-600 px-1 -mt-8'>Pay Now</span>
                        <button onClick={() => { handlePopUp(row, 'pay') }} className='bg-green-600 rounded-md font-bold text-lg text-white p-1.5 flex items-center space-x-2'>
                            <PiCurrencyDollarDuotone className=' font-bold' />
                        </button>
                    </div> 
                    <div className='has-tooltip'>
                        <span className='tooltip rounded shadow-lg p-1 bg-gray-100 text-red-600 px-1 -mt-8'>Close Deal</span>
                        <button onClick={() => { handlePopUp(row, 'close') }} className='bg-red-700 rounded-md font-bold text-lg text-white p-1.5 flex items-center space-x-2'>
                            <ImCross className=' font-bold' />
                        </button>
                    </div> 
                </>
                : <></>
            }
        </div>
    }

    const getStatus = (status) => {
        return <span className={`${statusColors[status]} rounded-full p-1 px-3 text-xs font-bold capitalize`}>{status.replaceAll('_', ' ').replaceAll('offer', '')}</span>
        // if (status == 'paid_transfer_completed') return <span className='text-green-500   p-1 rounded-full px-3 text-xs font-bold capitalize'>{status.replaceAll('_', ' ').replaceAll('offer', '')}</span>
        // else if (status == 'waiting') return <span className='text-yellow-500   p-1 rounded-full px-3 text-xs font-bold capitalize'>{status}</span>
        // else if (status == 'offer_accepted') return <span className='text-yellow-500   p-1 rounded-full px-3 text-xs font-bold capitalize'>{status.replaceAll('_', ' ').replaceAll('offer', '')}</span>
        // else if (status == 'counter_offer') return <span className='text-yellow-500   p-1 rounded-full px-3 text-xs font-bold capitalize'>{status.replaceAll('_', ' ')}</span>
        // else if (status == 'user_counter_offer') return <span className='text-yellow-500   1 rounded-full px-3 text-xs font-bold capitalize'>{status.replaceAll('_', ' ')}</span>
        // else if (status == 'paid') return <span className='text-yellow-500   p-1 rounded-full px-3 text-xs font-bold capitalize'>Offer Paid</span>
        // else return <span className='text-yellow-500   p-1 rounded-full px-3 text-xs font-bold capitalize'>{status.replaceAll('_', ' ').replaceAll('offer', '')}</span>
    }

    const customStyles = {
        headCells: {
            style: {
                color: "#111111",
                fontSize: '15px',
            },
        },
        rows: {
            style: {
                minHeight: '34px',
                fontSize: '14px',
                '&:not(:last-of-type)': {
                    borderBottomStyle: 'solid',
                    borderBottomWidth: '1px',
                    borderBottomColor: 'rgb(243 244 246 / 1)',
                },
            },
            highlightOnHoverStyle: {
                borderBottomColor: '#FFFFFF',
                outline: '1px solid #FFFFFF',
                background: '#d5e4ea'
            },
        },
        pagination: {
            style: {
                border: 'none',
            },
        },
    };

    const doSearch = (e) => {
        e = (e != '') ? e : undefined;
        setSearch(e);

        router.get(route('offers'),{
            ...route().params,
            search: e
        },{
            preserveScroll:true,
            preserveState:true
        })
    }

    useEffect(() => {
        setOffers(myoffers)
    }, [myoffers])

    return (
        <UserLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4">

                <OfferHistoryPopup getStatus={getStatus} showModal={showModal} modal={modal} domain={domain} />
                <CounterOfferPopup showModal={showCOModal} modal={comodal} domain={domain} offers={offers} setOffers={setOffers} />
                <ConfirmPopUp showModal={showCModal} status={status} offers={offers} setOffers={setOffers} modal={cmodal} domain={domain} />

                <div className='flex justify-start'>
                    <div>
                        <div className='text-3xl font-semibold mb-3'>
                            Marketplace Offers
                        </div>
                        <span className='text-gray-500 max-w-lg'>
                            View and Manage Marketplace Offers
                        </span>
                    </div>
                </div>

                <div className="flex justify-start">
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={limit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>
                <div className='flex justify-between'>
                    <div className="flex items-center space-x-2">
                        <div className="flex items-center">
                            <MdOutlineFilterAlt />
                            <span className="ml-2 text-sm text-gray-600">Filter:</span>
                        </div>
                        <Filter />
                    </div>
                    <div className="flex w-72 gap-3 ml-4">
                        <label htmlFor="search" className="pt-2">
                            Search:{" "}
                        </label>
                        <input
                            value={search}
                            onChange={(e) => {
                                doSearch(e.target.value);
                            }}
                            id="search"
                            type="text"
                            className="max-w-xs w-full border border-gray-300 rounded-md"
                            placeholder="Search domain"
                        />
                    </div>
                </div>
                <div className="mt-1">
                    <DataTable
                        key={limit}
                        columns={columns}
                        data={offers}
                        pagination
                        persistTableHead
                        highlightOnHover
                        customStyles={customStyles}
                        pointerOnHover
                        fixedHeader
                        fixedHeaderScrollHeight="600px"
                        paginationPerPage={limit}
                    />
                </div>
            </div>
        </UserLayout>
    )
}
/*

*/
