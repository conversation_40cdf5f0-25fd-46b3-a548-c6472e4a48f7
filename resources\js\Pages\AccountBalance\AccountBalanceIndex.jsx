//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'

//* ICONS
//...

//* COMPONENTS
import AccountCenterLayout from "@/Layouts/AccountCenterLayout";

//* PARTIALS
import PartialSectionAddFunds from './Partials/PartialSectionAddFunds';
import PartialSectionFunding from './Partials/PartialSectionFunding';
import PartialSectionHistory from './Partials/PartialSectionHistory';

//* STATE
//...

//* HOOKS 
//... 

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function AccountCreditIndex(
    { 
        fundItems,
        balance = 0,
        transactionItems
    }
)
{

    return (
        <AccountCenterLayout>
            <div
                className="
                    mx-auto container max-w-[900px] mt-20
                    flex flex-col gap-8 divide-y
                "
            >
                <div
                    className="text-2xl font-semibold"
                >
                    My Account Balance
                </div>
                <div>
                    <PartialSectionAddFunds
                        balance={balance}
                    />
                </div>
                <div>
                    <PartialSectionFunding
                        items={fundItems}
                        noTransaction={(transactionItems.length === 0)}
                    />
                </div>
                <div>
                    <PartialSectionHistory
                        list={transactionItems}
                    />
                </div>
            </div>
        </AccountCenterLayout>
    );
}
