<?php

namespace App\Modules\PaymentSummary\Services;

use App\Exceptions\UserDomainException;
use App\Models\PaymentSummary;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\PaymentSummary\Requests\ShowListRequest;
use App\Traits\CursorPaginate;
use App\Util\Helper\CryptHelper;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class ViewPaymentSummaryService
{
    use CursorPaginate;

    private $pageLimit = 20;

    public static function instance(): self
    {
        $viewPaymentSummaryService = new self;

        return $viewPaymentSummaryService;
    }

    public function getIndex(ShowListRequest $request)
    {
        return $this->getSummaryAllList($request);
    }

    public function getById(int $summaryId, int $userId)
    {
        return DB::table('payment_summaries')
            ->where('payment_summaries.user_id', $userId)
            ->where('payment_summaries.id', $summaryId)
            ->get()
            ->first();
    }

    public function getView(Request $request)
    {
        $summary = $this->getQuery()
            ->where('payment_summaries.id', $request->id)
            ->first();        

        if (! $summary) {
            throw new UserDomainException(404, 'Page not found.', '');
        }
                
        switch ($summary->type) {
            case PaymentSummaryType::PAYMENT_INVOICE:
                return $this->getPaymentView($summary->payment_invoice_id, $summary->user_id, $summary);
            case PaymentSummaryType::PAYMENT_REIMBURSEMENT:
                return $this->getRefundView($summary->payment_invoice_id, $summary->user_id, $summary);
            case PaymentSummaryType::MARKETPLACE_INVOICE:
                return $this->getPaymentView($summary->payment_market_place_invoice_id, $summary->user_id, $summary);
            case PaymentSummaryType::MARKETPLACE_REIMBURSEMENT:
                return $this->getRefundView($summary->payment_market_place_invoice_id, $summary->user_id, $summary);
            case PaymentSummaryType::ACCOUNT_BALANCE:
                return $this->getPaymentView($summary->payment_service_id, $summary->user_id, $summary);
            case PaymentSummaryType::MULTI_CHECKOUT_INVOICE:
                return $this->getPaymentView($summary->id, $summary->user_id, $summary);
            default:
                return $this->getPaymentView($summary->id, $summary->user_id, $summary);
        }
    }

    // PRIVATE FUNCTIONS

    private function getPaymentView(int $id, int $userId, object $paymentSummary)
    {
        $data                           = PaymentSummaryService::instance()->getPayment($id, $userId, $paymentSummary->type);
        $paymentSummary->transaction_id = ($paymentSummary->transaction_id) ? CryptHelper::decrypt($paymentSummary->transaction_id) : '';

        $data['summaryData'] = $paymentSummary;

        if ($paymentSummary->source == "BANK_TRANSFER")
        {
            $bankTransfer = PaymentSummary::query()
                ->select(
                    'bank_transfers.id as bankTransferId',
                    'bank_transfers.purpose as bankTransferPurpose',
                    'bank_transfers.reference_number as bankTransferReferenceNumber'
                )
                ->where('payment_summaries.id', '=', $paymentSummary->id)
                ->where('payment_summaries.user_id', '=', $userId)
                ->leftJoin('payment_services', 'payment_services.id', '=', 'payment_summaries.payment_service_id')
                ->leftJoin('bank_transfers', 'bank_transfers.id', '=', 'payment_services.bank_transfer_id')
                ->firstOrFail();

            $bankTransferData = 
            [
                'id'              => $bankTransfer->bankTransferId,
                'purpose'         => $bankTransfer->bankTransferPurpose,
                'referenceNumber' => $bankTransfer->bankTransferReferenceNumber
            ];

            $data['summaryData']->bankTransferData = $bankTransferData;
        }

        return Inertia::render('Payment/ShowPayment', $data);
    }

    private function getRefundView(int $id, int $userId, object $summary)
    {
        $data = PaymentSummaryService::instance()->getRefund($id, $userId, $summary->type);
        $data['summaryData'] = $summary;

        // dd($data);
        return Inertia::render('Payment/ShowRefund', ['item' => $data]);
    }

    private function getQuery()
    {
        return DB::table('payment_summaries')
            ->where('payment_summaries.user_id', $this->getUserId());
    }

    private function getSummaryAllList(ShowListRequest $request)
    {
        // DB::enableQueryLog();
        $builder = $this->getQuery();
        $this->whenHasOrderbyPaymentSummary($builder, $request);
        $this->whenHasFilterByTypePaymentSummary($builder,$request);
        $this->whenHasFilterBySourcePaymentSummary($builder,$request);
        $this->pageLimit = $request->input('limit', 20);
        $builder = $builder->select('*')
            ->paginate($this->pageLimit)->withQueryString();
        // dd(DB::getQueryLog());

        $others = [
            'refund_list' => [],
            'status_type' => [],
        ];

        return CursorPaginate::cursor($builder, $this->paramToURI($request), $others);
    }

    private function whenHasFilterBySourcePaymentSummary(Builder $builder, ShowListRequest $request) {
        $builder->when($request->filled('source'),function (Builder $query) use ($request){

            $key = ($request->input('source') == 'Card') ? 'STRIPE' : str_replace(" ","_",strtoupper($request->input('source')));

            $query->where('payment_summaries.source',$key);
            
        });
    }

    private function whenHasFilterByTypePaymentSummary(Builder $builder, ShowListRequest $request){
        $builder->when($request->filled('type'),function(Builder $query) use ($request) {
            
            if($request->input('type') != 'Multi Checkout Invoice'){
                $key = array_search($request->input('type'),PaymentSummaryType::TEXT,true);
            }else {
                $key = strtoupper($request->input('type'));
                $key = str_replace(' ','_',$key);
            }

            $query->where('payment_summaries.type',$key);
        });
    }

    private function whenHasOrderbyPaymentSummary(Builder &$builder, ShowListRequest $request)
    {
        $builder->when($request->has('orderby'), function (Builder $query) use ($request) {
            $orderby = explode(':', $request->orderby);

            if (count($orderby) == 2 && in_array($orderby[1], [' Asc', ' Desc'])) {
                switch ($orderby[0]) {
                    case 'Created':
                        $query->orderBy('payment_summaries.created_at', trim($orderby[1]));

                    case 'Type':
                        $query->orderBy('payment_summaries.type', trim($orderby[1]));

                    case 'Amount':
                        $query->orderBy('payment_summaries.paid_amount', trim($orderby[1]));
                    default:
                        $query->orderBy('payment_summaries.id', 'desc');
                }
            } else {
                $query->orderBy('payment_summaries.id', 'desc');
            }
        })
            ->when(! $request->has('orderby'), function (Builder $query) {
                $query->orderBy('payment_summaries.id', 'desc');
            });
    }

    private function paramToURI(ShowListRequest $request): array
    {
        $param = [];

        if ($request->has('orderby')) {
            $param[] = 'orderby='.$request->orderby;
        }

        return $param;
    }

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }
}
