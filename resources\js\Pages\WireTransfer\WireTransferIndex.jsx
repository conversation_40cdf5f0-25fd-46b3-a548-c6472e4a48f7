//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { MdOutlineFilterAlt } from "react-icons/md";
import { ImSortAlphaAsc, ImSortAlphaDesc } from "react-icons/im";
import { TbSortAscending2, TbSortDescending2 } from "react-icons/tb";

//* COMPONENTS
import AccountCenterLayout from "@/Layouts/AccountCenterLayout";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import Item from "../../Components/WireTransfer/Item";
import Filter from "../../Components/WireTransfer/Filter";

//* PARTIALS
import PartialWireTransferTableRowItem from './Partials/PartialWireTransferTableRowItem';

//* STATE
//...

//* HOOKS 
//... 

//* UTILS
import { getEventValue } from "@/Util/TargetInputEvent";


//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function WireTransferIndex(
    {
        items,
        onFirstPage,
        onLastPage,
        nextPageUrl,
        previousPageUrl,
        itemCount = 0,
        total = 0,
    }
)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    //...
    
    //! VARIABLES
    const SORT_TYPE =
    {
        NAME_ASC    : "Name: Asc",
        NAME_DESC   : "Name: Desc",
        CREATED_ASC : "Date Created: Asc",
        CREATED_DESC: "Date Created: Desc",
        // UPDATED_ASC : "updated:asc",
        // UPDATED_DESC: "updated:desc",
    };
    
    const paramOrderBy = route().params.orderby;
    const query        = route().params;
    const limit        = parseInt(query.limit) || 20;

    //! STATES
    const [selectedItems, setSelectedItems] = useState([]);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const handleItemCheckboxChange = (itemId, e) => {
        if (getEventValue(e)) {
            setSelectedItems((prevSelectedItems) => {
                return [...prevSelectedItems, itemId];
            });
            setSelectAll(() => items.length === selectedItems.length + 1);
        } else {
            setSelectedItems((prevSelectedItems) =>
                prevSelectedItems.filter((id) => id !== itemId)
            );
            setSelectAll(false);
        }
    };

    const handleSortOrder = (sortOrder) => {
        let payload = {};

        payload.orderby = sortOrder;

        router.get(route("wire.transfer.index"), payload);
    };

    const handleLimitChange = (e) => {
        router.get(
            route("wire.transfer.index"),
            {
                ...route().params, // preserve existing query params
                limit: e.target.value,
                page: 1,
            },
            {
                preserveState: true,
                preserveScroll: true,
                replace: true,
            }
        );
        //console.log(items);
    };

    return (
        <AccountCenterLayout>
            <div className="mx-auto container max-w-[1100px] mt-20 flex flex-col space-y-4">
                <div className="flex items-center space-x-4 text-gray-700 text-md font-semibold">
                    <div className="pt-2 pb-8">
                        <span className="text-2xl font-semibold">
                            Wire Transfer
                        </span>
                    </div>
                </div>
                <div className="flex justify-start">
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={limit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>
                <div
                    id="sample"
                    className="flex items-center space-x-2 flex-wrap min-h-[2rem]"
                >
                    <label className="flex items-center">
                        <MdOutlineFilterAlt />
                        <span className="ml-2 text-sm text-gray-600">
                            {" "}
                            Filter:{" "}
                        </span>
                    </label>
                    <Filter />
                </div>
                <div>
                    <table className="min-w-full text-left border-spacing-y-2.5 border-separate">
                        <thead className=" bg-gray-50 text-sm">
                            <tr>
                                <th>
                                    <span>Reference No.</span>
                                </th>
                                <th
                                    className="py-3"
                                >
                                    <label className="flex items-center pl-2 space-x-2">
                                        <span>Name</span>
                                        <button
                                            onClick={() =>
                                                handleSortOrder(
                                                    paramOrderBy ===
                                                        SORT_TYPE.NAME_ASC
                                                        ? SORT_TYPE.NAME_DESC
                                                        : SORT_TYPE.NAME_ASC
                                                )
                                            }
                                            disabled={items.length === 0}
                                        >
                                            {paramOrderBy ===
                                            SORT_TYPE.NAME_ASC ? (
                                                <ImSortAlphaAsc />
                                            ) : (
                                                <ImSortAlphaDesc />
                                            )}
                                        </button>
                                    </label>
                                </th>
                                <th>
                                    <span>Company</span>
                                </th>
                                <th>
                                    <span>Amount ($)</span>
                                </th>
                                <th>
                                    <span>Purpose</span>
                                </th>
                                <th>
                                    <span>Status</span>
                                </th>
                                <th>
                                    <label className="flex items-center space-x-2">
                                        <span>Date Created</span>
                                        <button
                                            onClick={() =>
                                                handleSortOrder(
                                                    paramOrderBy ===
                                                        SORT_TYPE.CREATED_ASC
                                                        ? SORT_TYPE.CREATED_DESC
                                                        : SORT_TYPE.CREATED_ASC
                                                )
                                            }
                                            disabled={items.length === 0}
                                        >
                                            {paramOrderBy ===
                                            SORT_TYPE.CREATED_ASC ? (
                                                <TbSortAscending2 />
                                            ) : (
                                                <TbSortDescending2 />
                                            )}
                                        </button>
                                    </label>
                                </th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {items.map((item, index) => (
                                <PartialWireTransferTableRowItem
                                    key={"ci-" + index}
                                    item={item}
                                    isSelected={selectedItems.includes(item.id)}
                                    onCheckboxChange={handleItemCheckboxChange}
                                />
                            ))}
                        </tbody>
                    </table>
                </div>
                <CursorPaginate
                    onFirstPage={onFirstPage}
                    onLastPage={onLastPage}
                    nextPageUrl={nextPageUrl}
                    previousPageUrl={previousPageUrl}
                    itemCount={itemCount}
                    total={total}
                    shouldPreserveState={true}
                />
            </div>
        </AccountCenterLayout>
    );
}
