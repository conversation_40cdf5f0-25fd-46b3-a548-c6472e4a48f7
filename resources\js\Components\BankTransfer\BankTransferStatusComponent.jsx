//* PACKAGES
import React from 'react';

//* ICONS
import { <PERSON>a<PERSON><PERSON>, FaRegThumbsUp, FaRegThumbsDown, FaRegHourglass, FaRegQuestionCircle } from 'react-icons/fa';

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* HOOKS
//...

//* UTILS
//... 

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...


export default function BankTransferStatusComponent(
    {
        item
    }
)
{
    //! PACKAGE
    //...    

    //! HOOKS
    //...

    //! VARIABLES
    //...

    //! STATES
    //... 

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const displayStatus = () =>
    {
        let classNamesCommon   = "flex items-center gap-2 uppercase font-semibold"
        let classNamesSpecific = ""
        let icon               = <FaRegHourglass />;
        let status             = 'pending';

        if (item.deleted_at) {
            classNamesSpecific = 'text-danger';
            icon = <FaRegThumbsDown />;
            status = "rejected";
        }
        else if (item.verified_at)
        {
            classNamesSpecific = 'text-success';
            icon = <FaRegThumbsUp />;
            status = "verified";
        }
        else if (item.reviewed_at && !item.deleted_at)
        {
            classNamesSpecific = 'text-orange-500';
            icon = <FaRegQuestionCircle />;
            status = "unverified";
        }
        else if (!item.reviewed_at && !item.deleted_at)
        {
            classNamesSpecific = 'text-slate-500';
            icon = <FaRegHourglass />;
            status = "pending";
        }

        return (
            <div
                className={`
                    ${classNamesCommon} 
                    ${classNamesSpecific}     
                `}
            >
                <span>
                    {status}
                </span>
                {icon}
            </div>
        );
    };

    return (
        <>
            {displayStatus()}   
        </>
    );
};
