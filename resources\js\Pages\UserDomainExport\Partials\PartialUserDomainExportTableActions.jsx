//* PACKAGES 
import React from 'react';
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";

//* ICONS
import { TbEraser, TbTrashX, TbDownload } from "react-icons/tb";

//* COMPONENTS
import AppButtonComponent from '@/Components/App/AppButtonComponent';

export default function PartialUserDomainExportTableActions(
    {
        //! STATES 
        paginationMeta,
        selectedItems,

        //! EVENTS
        setSelectedItems
    }
) {
    //! VARIABLES 
    const iconSizeClass = 'h-5 w-5';

    const itemsAction =
    [
        {
            label       : 'Download All',
            icon        : <TbDownload className={`${iconSizeClass}`} />,
            isProcessing: paginationMeta.total == 0,
            isDisabled  : paginationMeta.total == 0,
            onClick: () =>
            {
                const url = route(
                    'user-domain-export.download-all',
                    {
                        'selectedItems': selectedItems,
                    }
                );

                window.location.href = url;
            }
        },
        {
            label: 'Download Selected',
            icon: <TbDownload className={`${iconSizeClass}`} />,
            isProcessing: selectedItems.length <= 0,
            isDisabled: selectedItems.length <= 0,
            onClick: () =>
            {
                if (selectedItems.length === 0) return;

                const url = route(
                    'user-domain-export.download-batch',
                    {
                        'selectedItems': selectedItems,
                    }
                );

                window.location.href = url;
            }
        },
        {
            label: 'Delete Selected',
            icon: <TbEraser className={`${iconSizeClass}`} />,
            isProcessing: selectedItems.length <= 0,
            isDisabled: selectedItems.length <= 0,
            onClick: () => {
                router.post(
                    route('user-domain-export.delete-batch'),
                    { selectedItems },
                    {
                        onSuccess: () => {
                            toast.success(`${selectedItems.length} item(s) deleted.`);
                            setSelectedItems([]);
                        },
                        onError: () => {
                            toast.error('Something went wrong!');
                        },
                    }
                )
            }
        },
        {
            label: 'Clear All',
            icon: <TbTrashX className={`${iconSizeClass}`} />,
            isProcessing: paginationMeta.total == 0,
            isDisabled: paginationMeta.total == 0,
            onClick: () => {
                router.delete(
                    route('user-domain-export.clear'),
                    {
                        onSuccess: () => {
                            toast.success("Items cleared.");
                            router.get(route('user-domain-export.index'));
                        },
                        onError: () => {
                            toast.error('Something went wrong!');
                        },
                    }
                )
            }
        },
    ];

    return (
        <div className="flex items-center gap-x-5 justify-center py-2 border-b-2">
            {
                itemsAction.map((item, index) => (
                    <AppButtonComponent
                        key={index}
                        label={item.label}
                        icon={item.icon}
                        isProcessing={item.isProcessing}
                        isDisabled={item.isDisabled}
                        handleOnClick={item.onClick}
                    />
                ))
            }
        </div>
    );
}
