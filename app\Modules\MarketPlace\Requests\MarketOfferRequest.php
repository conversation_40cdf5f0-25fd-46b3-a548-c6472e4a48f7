<?php

namespace App\Modules\MarketPlace\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class MarketOfferRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'orderby' => ['nullable','string',Rule::in([
                "Domain: Asc",
                "Domain: Desc",
                "Initial Offer: Asc",
                "Initial Offer: Desc",
                "Last Status Update: Asc",
                "Last Status Update: Desc",
                "Buy Now Price: Asc",
                "Buy Now Price: Desc"
            ])],
            'status' => ['nullable','string',Rule::in([
                "Waiting",
                "Counter Offer",
                "Offer Accepted",
                "Offer Rejected",
                "Offer Closed",
                "Paid",
                "Paid Hold Pending",
                "Paid Order Pending",
                "Paid Transfer Pending",
                "Paid Transfer Requested",
                "Paid Transfer Completed",
                "User Counter",
            ])],
            'search' => ['nullable','string']
        ];
    }
}
