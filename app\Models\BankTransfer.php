<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str; 

class BankTransfer extends Model
{

    /** @use HasFactory<\Database\Factories\BankTransferFactory> */
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'account_name',
        'gross_amount',
        'net_amount',
        'service_fee',
        'note',
        'verified_at',
        'company',
        'purpose', 
        'reference_number'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(
            function ($transaction) 
            {
                $prefix = match ($transaction->purpose) 
                {
                    'add credit'          => 'AC',
                    'marketplace payment' => 'MP',
                    'offer payment'       => 'OP',
                    default               => 'TRX', // fallback
                };

                $transaction->reference_number = self::generateReferenceNumber($prefix);
            }
        );
    }

public static function generateReferenceNumber($prefix = 'TRX')
{
    do 
    {
        $reference = $prefix . "-" . now()->format('Ymd') . "-". strtoupper(Str::random(12));
    } 
    while (self::where('reference_number', $reference)->exists());

    return $reference;
}
}
