import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import useOutsideClick from "@/Util/useOutsideClick";
import { useRef, useState } from "react";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";

export default function Filter() {
    const currentParams = route().params;

    const filterConfig = {
        container: {
            active: false,
            reload: false,
        },
        field: {
            orderby: {
                active: false,
                value: currentParams.orderby ? [currentParams.orderby] : [],
                type: "option",
                items: [
                    "Domain: Asc",
                    "Domain: Desc",
                    "Initial Offer: Asc",
                    "Initial Offer: Desc",
                    "Last Status Update: Asc",
                    "Last Status Update: Desc",
                    "Buy Now Price: Asc",
                    "Buy Now Price: Desc"
                    
                ],
                name: "Order By",
            },
            status: {
                active: false,
                value: currentParams.status ? [currentParams.status] : [],
                type: "option",
                items: [
                    "Waiting",
                    "Counter Offer",
                    "Offer Accepted",
                    "Offer Rejected",
                    "Offer Closed",
                    "Paid",
                    "Paid Hold Pending",
                    "Paid Order Pending",
                    "Paid Transfer Pending",
                    "Paid Transfer Requested",
                    "Paid Transfer Completed",
                    "User Counter",
                ],
                name: "Status",
            },
        },
    };

    const [filter, setFilter] = useState(filterConfig);
    const ref = useRef();
    const { field } = filter;

    useOutsideClick(ref, (event) => {
        if (filter.container.active) {
            setFilter({
                ...filter,
                container: { ...filter.container, active: false }
            });
        }

        const updatedField = { ...field };
        let hasChanges = false;
        
        Object.keys(updatedField).forEach(key => {
            if (updatedField[key].active) {
                const dropdownElement = document.querySelector(`[data-filter-key="${key}"]`);
                if (!dropdownElement?.contains(event.target)) {
                    updatedField[key].active = false;
                    hasChanges = true;
                }
            }
        });

        if (hasChanges) {
            setFilter({
                ...filter,
                field: updatedField
            });
        }
    });

    const handleDisplayToggle = (newObject) => {
        setFilter({ ...filter, ...newObject });
    };

    const handleFieldUpdateValue = (key, value) => {
        const newFilter = {
            ...filter,
            container: { ...filter.container, active: false, reload: true },
            field: {
                ...filter.field,
                [key]: {
                    ...filter.field[key],
                    value: [value],
                    active: false
                },
            },
        };
        setFilter(newFilter);
    
        const payload = {
            ...currentParams,
            [key]: value
        };

        toast.info("Reloading data, please wait...");

        router.get(route("offers"), payload);
    };

    return (
        <div className="flex items-center relative">
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div ref={ref}>
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />
                <div className="relative">
                    <OptionFilter
                        fieldProp={field.orderby}
                        fieldKey="orderby"
                        handleFieldUpdateValue={handleFieldUpdateValue}
                        data-filter-key="orderby"
                    />
                    <OptionFilter
                        fieldProp={field.status}
                        fieldKey="status"
                        handleFieldUpdateValue={handleFieldUpdateValue}
                        data-filter-key="status"
                    />
                </div>
            </div>
        </div>
    );
}
