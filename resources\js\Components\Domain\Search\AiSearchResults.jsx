import React, { useState, useEffect, useRef } from "react";
import SearchNoDomainFound from "./SearchNoDomainFound";
import SearchDomainNow from "./SearchDomainNow";
import { usePage } from "@inertiajs/react";
import { IoMdSearch } from 'react-icons/io';
import PrimaryButton from "@/Components/PrimaryButton";
import { toast } from "react-toastify";
import CartCounterState from "@/State/CartCounterState";
import OfferPopUp from "./components/OfferPopUp";

export default function AiSearchResults({ auth, searchResult, cart, setCart, fees, modal, showModal, offer, showOffer, offerPrice }) {
    const { setCartCounter } = CartCounterState();

    const [aiFilters, setAiFilters] = useState({
        subcategory: 'all',
        model: 'gpt41mini'
    });

    const [isLoading, setIsLoading] = useState(false);

    const processedResultsRef = useRef({
        categoryGroups: {},
        subcategories: []
    });

    const getDomainPrice = (item) => {
        const extension = (item.domain_name).split('.').pop();

        // if(item.in_afternic) return (item.price != null ? item.price : fees[extension].price)
        if(item.in_afternic) return ((item.price != null && item.price > 0.00) ? item.price : item.min_offer)

        return fees[extension].price
    };

    const isDomainInCart = (domainName) => {
        return cart.includes(domainName);
    };

    const handleAddToCart = (item) => {
        setIsLoading(true);

        if (item.in_afternic) addToMarketCart(item)
        else addToEPPCart(item)
    };

    const addToEPPCart = (item) => {
        const payload = {
            'name': item.domain_name,
            'extension': (item.domain_name).split('.').pop()
        };

        axios.post(route("mycart.store"), [payload])
            .then((response) => {
                setIsLoading(false);
                toast.success('Added to cart.');
                setCartCounter(cart.length + 1);
                setCart(prev => [...prev, item.domain_name]);
            })
            .catch((error) => {
                setIsLoading(false);
                setCart(prev => [...prev, item.domain_name]);
                toast.error(error.response.data.message ?? 'Something went wrong!');
            })
    }

    const addToMarketCart = (item) => {
        // setIsLoading(false);
        // toast.error('Currently unavailable');

        const payload = {
            'user_id': auth.user.id,
            'tld_id': fees[item.domain_name.split('.').pop()].tld_id,
            'name': item.domain_name,
            'price': item.price,
            'is_fast_transfer': item.fast_transfer ? 1 : 0,
            'vendor': item.in_afternic ? 'afternic' : '',
        };

        axios.post(route("mycart.market"), payload)
            .finally(() => {
                setIsLoading(false);
                toast.success('Added to cart.');
                setCartCounter(cart.length + 1);
                setCart(prev => [...prev, item.domain_name]);
            })
    }

    const getButton = (b) => {
        if (isDomainInCart(b.domain_name)) return <span className="block px-3.5 py-2.5 bg-gray-700 border border-transparent rounded-md font-semibold text-sm text-white tracking-widest bg-opacity-30">
            Added to cart
        </span>

        if(b.price == 0.00) return <button onClick={() => { showOffer(b.domain_name, b.min_offer) }} className="disabled:opacity-50  py-2 px-[18px] text-xs flex items-center bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-900 border border-transparent rounded-md font-bold tracking-widest">
            Make Offer
        </button>

        return <PrimaryButton
            processing={isLoading}
            type="button"
            onClick={() => handleAddToCart(b)}
            className="disabled:opacity-50 py-1 px-3 text-xs flex items-center"
        >
            Add to Cart
        </PrimaryButton>
    }

    useEffect(() => {
        if (!searchResult || searchResult.length === 0) return;
    }, [searchResult])

    if (searchResult == undefined) return <SearchDomainNow />;
    else if (searchResult.length == 0) return <SearchNoDomainFound />;

    return (
        <div className="mx-auto container max-w-[900px] flex flex-col">

            <OfferPopUp modal={modal} showModal={showModal} offer={offer} min_offer={offerPrice} />

            <div className="grid grid-cols-2 gap-8 mt-10">
                {Object.entries(searchResult)
                    .map((a, i) => (
                        (a[1].domains.length <= 0) ? <></> : <>
                            <div className="mb-8" key={i}>
                                <h2 className="uppercase text-base font-semibold text-gray-800 border-b border-gray-100 pb-2 mb-4">
                                    {a[0].replaceAll('_', ' ')}
                                </h2>
                                
                                <div className="flex flex-col">

                                    {a[1].domains.map((b, i) => (
                                        <div key={i} className="flex justify-between items-center py-2 border-b border-gray-50">
                                            <div className="flex flex-col">
                                                <span className="text-gray-800">{b.domain_name}</span>
                                                <div className="flex items-center">
                                                    {b.in_afternic && b.price <= 0 && b.min_offer > 0 && (
                                                        <span className="-ml-2 bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-800 font-medium text-sm px-2 py-0.5 rounded-full">
                                                            <span className="text-sm"> ${(getDomainPrice(b)).toLocaleString('en', { useGrouping: true })} </span>
                                                            -
                                                            <span className="text-xs"> Minimum Offer </span>
                                                        </span>
                                                    )}
                                                    {b.in_afternic && b.price <= 0 && b.min_offer == 0 && (<span className="-ml-2 bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-800 font-medium text-xs px-2 py-0.5 rounded-full">
                                                            No Minimum Offer
                                                        </span>
                                                    )}
                                                    {(b.in_afternic && b.price > 0 && (b.min_offer <= 0 || b.min_offer != null)) && (
                                                        <>
                                                            <span className="text-gray-700">${(getDomainPrice(b)).toLocaleString('en', { useGrouping: true })}</span>
                                                            <span className="ml-2 bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-800 text-xs px-2 py-0.5 rounded-full font-medium border border-yellow-200 shadow-sm flex items-center">
                                                                Premium
                                                            </span>
                                                        </>
                                                    )}
                                                    {b.in_afternic && b.fast_transfer == 1 && (
                                                        <>
                                                            <span className="ml-1 bg-blue-50 text-primary font-medium text-xs px-2 py-0.5 rounded-full">
                                                                Fast Transfer
                                                            </span>
                                                        </>
                                                    )}
                                                    {!b.in_afternic && (
                                                        <span className="text-gray-700">${(getDomainPrice(b)).toLocaleString('en', { useGrouping: true })}</span>
                                                    )}
                                                </div>
                                            </div>

                                            { getButton(b) }
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </>
                    )
                )}
            </div>
        </div>
    );
} 