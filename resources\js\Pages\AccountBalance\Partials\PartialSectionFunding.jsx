//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'

//* ICONS
//...

//* COMPONENTS
import BankTransferStatusComponent from '@/Components/BankTransfer/BankTransferStatusComponent';

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
//... 

//* UTILS
import setDefaultDateFormat from '@/Util/setDefaultDateFormat';

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialSectionFunding(
    {
        items = [],
        noTransaction = false
    }
)
{
    return (
        <div
            className='flex flex-col gap-4'
        >
            <div className="pt-4">
                <span className="text-2xl text-gray-700 font-semibold"> Funding</span>
            </div>
            <div className="mx-auto container max-w-[1200px]">
                {(items.length === 0 && noTransaction) ? (
                    <div className="flex justify-center">
                        <span className="text-xl">No funds yet.</span>
                    </div>
                ) : (items.length === 0 && !noTransaction) ? (
                    <div className="flex justify-center">
                        <span className="text-xl">No pending funds.</span>
                    </div>
                ) : (
                    <div
                        className="w-full"
                    >
                        <table 
                            className="min-w-full text-left border-spacing-y-2.5 border-separate"
                        >
                            <thead 
                                className="bg-gray-50 text-sm"
                            >
                                <tr
                                    className=''
                                >
                                    <th    
                                        className='py-3'
                                    >
                                        Reference No.
                                    </th>
                                    <th 
                                        className=""
                                    >
                                        Amount ($)
                                    </th>
                                    <th className="">
                                        Status
                                    </th>
                                    <th className="">
                                        Date Created
                                    </th>
                                </tr>
                            </thead>
                            <tbody 
                                className="text-sm"
                            >
                                {
                                    items.map(
                                    (item, index) =>
                                        (
                                            <tr
                                                key={"fi-" + index}
                                                className="bg-white mb-2"
                                            >
                                                <td
                                                    className='font-semibold'
                                                >
                                                    {item.reference_number}
                                                </td>
                                                <td
                                                    className=""
                                                >
                                                    {parseFloat(item.amount).toFixed(2)}
                                                </td>
                                                <td>
                                                    <BankTransferStatusComponent
                                                        item={item}
                                                    />
                                                </td>
                                                <td>
                                                    {setDefaultDateFormat(item.created_at) + ' ' + new Date(item.created_at + 'Z').toLocaleTimeString()}
                                                </td>
                                            </tr>
                                        )
                                    )
                                }
                            </tbody>
                        </table>
                    </div>
                )}
            </div>
        </div>
    );
};
