//* PACKAGES
import React from 'react';

//* ICONS
//...

//* COMPONENTS
import { HistoryItem } from '@/Components/AccountBalance/Items/HistoryItem';

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
//... 

//* UTILS
import CursorPaginate from '@/Components/Util/CursorPaginate';

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...


export default function PartialSectionHistory(
    {
        list = []
    }
)
{
    // Check if list is an object with items property or just an array
    const items = Array.isArray(list) ? list : (list.items || []);
    const hasPagination = !Array.isArray(list) && list.items;

    return (
        <div
            className='flex flex-col gap-4'
        >
            <div className="pt-4">
                <span className="text-2xl text-gray-700 font-semibold">Transaction History</span>
            </div>
            <div className="mx-auto container max-w-[1200px]">
                {/* <div className="flex items-center space-x-2 flex-wrap min-h-[2rem] mb-4">
                    <label className="flex items-center">
                        <MdOutlineFilterAlt />
                        <span className="ml-2 text-sm text-gray-600">Filter: </span>
                    </label>
                    <Filter />
                </div> */}

                {items.length === 0 ? (
                    <div className="flex justify-center">
                        <span className="text-xl">No transactions yet.</span>
                    </div>
                ) : (
                    <div className="w-full">
                        <table className="min-w-full text-left border-spacing-y-2.5 border-separate">
                            <thead className="bg-gray-50 text-sm">
                                <tr>
                                    <th className="w-[10%] py-3">Balance ($)</th>
                                    <th className="w-[10%] py-3">Amount ($)</th>
                                    <th className="w-[10%] py-3">Fees ($)</th>
                                    <th className="w-[15%] py-3">Total Paid ($)</th>
                                    <th className="w-[30%] py-3">Description</th>
                                    <th className="w-[25%] py-3">Date Created</th>
                                </tr>
                            </thead>
                            <tbody className="text-sm">
                                {items.map((item, index) => (
                                    <HistoryItem
                                        key={"hi-" + index}
                                        index={index}
                                        item={item} />
                                ))}
                            </tbody>
                        </table>

                        {hasPagination && (
                            <div className="mt-4">
                                <CursorPaginate
                                    onFirstPage={list.onFirstPage}
                                    onLastPage={list.onLastPage}
                                    nextPageUrl={list.nextPageUrl}
                                    previousPageUrl={list.previousPageUrl}
                                    itemCount={list.itemCount}
                                    total={list.total}
                                    itemName={list.itemName}
                                    shouldPreserveState={true}
                                />
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};
