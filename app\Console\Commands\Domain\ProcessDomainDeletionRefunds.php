<?php

namespace App\Console\Commands\Domain;

use Illuminate\Console\Command;
use App\Modules\Domain\Services\DomainDeletionService;
use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Support\Facades\DB;

class ProcessDomainDeletionRefunds extends Command
{
    protected $signature = 'domain:deletion-refund';

    protected $description = 'Process refunds for all approved domain deletions';

    public function handle()
    {
        $refundLimit = $this->getRefundLimit();
        if (!$refundLimit) {
            return 1;
        }

        $domains = $this->getRefundableDomains();
        if (!$this->hasDomainsToProcess($domains)) {
            return 0;
        }

        $this->processRefunds($refundLimit, $domains);

        $this->info('Domain deletion refunds processed successfully.');
        return 0;
    }

    private function getRefundLimit()
    {
        $refundLimit = DB::table('domain_registration_refund_limit')->first();

        return $refundLimit;
    }

    private function getRefundableDomains(): array
    {
        return [
            'registration' => DomainDeletionService::instance()->getRefundableRegistrationDomains(),
            'renewal' => DomainDeletionService::instance()->getRefundableRenewalDomains()
        ];
    }

    private function hasDomainsToProcess(array $domains): bool
    {
        if (empty($domains['registration']) && empty($domains['renewal'])) {
            app(AuthLogger::class)->info('ProcessDomainDeletionRefunds: No eligible refunds to process.');
            return false;
        }

        return true;
    }

    private function processRefunds($refundLimit, array $domains): void
    {
        $isRegistrationLimitReached = $refundLimit->times_triggered >= $refundLimit->limit;

        $this->processRegistrationRefunds($domains['registration'], $isRegistrationLimitReached);
        $this->processRenewalRefunds($domains['renewal']);
    }

    private function processRegistrationRefunds(array $registrationRefunds, bool $isLimitReached): void
    {
        if (empty($registrationRefunds)) {
            return;
        }

        if ($isLimitReached) {
            app(AuthLogger::class)->info('ProcessDomainDeletionRefunds: Refund limit reached. Marking ' . count($registrationRefunds) . ' registration domains as refunded without processing refund');
            $this->markDomainsAsRefundedWithoutRefund($registrationRefunds, 'is_refunded');
        } else {
            app(AuthLogger::class)->info('ProcessDomainDeletionRefunds: Processing ' . count($registrationRefunds) . ' registration refunds...');
            DomainDeletionService::instance()->refundRegistration($registrationRefunds);
        }
    }

    private function processRenewalRefunds(array $renewalRefunds): void
    {
        if (empty($renewalRefunds)) {
            return;
        }

        app(AuthLogger::class)->info('ProcessDomainDeletionRefunds: Processing ' . count($renewalRefunds) . ' renewal refunds...');
        DomainDeletionService::instance()->refundRenewal($renewalRefunds);
    }

    private function markDomainsAsRefundedWithoutRefund(array $domains, string $field): void
    {
        foreach ($domains as $domain) {
            try {
                $registeredDomainId = $domain->registered_domain_id;

                DB::table('domain_cancellation_requests')
                    ->where('registered_domain_id', $registeredDomainId)
                    ->update([
                        $field => true,
                        'refunded_at' => now(),
                        'support_agent_id' => null,
                    ]);

                app(AuthLogger::class)->info("Marked domain as refunded without processing refund for domain name: {$domain->domain_name}");

            } catch (\Exception $e) {
                app(AuthLogger::class)->error("Failed to mark domain as refunded for domain name: {$domain->domain_name} - ".$e->getMessage());
            }
        }
    }
}
