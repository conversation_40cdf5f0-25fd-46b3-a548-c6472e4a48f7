<?php

namespace App\Modules\BankTransfer\Services;

use App\Events\EmailSent;
use App\Mail\Constants\MailConstant;
use App\Mail\Payment\BankTransferMail;
use App\Models\User;
use App\Modules\BankTransfer\Constants\BankTransferStatus;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Util\Constant\QueueConnection;
use Exception;
use Illuminate\Support\Facades\Mail;

class BankTransferMailService
{
    public static function instance(): self
    {
        $bankTransferMailService = new self;

        return $bankTransferMailService;
    }

    public function sendPendingMail(object $bankTransfer, int $userId)
    {
        $user = User::findOrFail($userId);
        $payload = [
            'email' => $user->email,
            'name' => $user->first_name.' '.$user->last_name,
            'user_id' => $userId,
            'bank_transfer' => $bankTransfer,
            'type' => BankTransferStatus::PENDING,
            'subject' => 'Bank Transfer Request is Pending - '.config('app.name'),
        ];

        $this->sendMail($payload);
    }

    public function sendMail(array $payload)
    {
        try {
            $queueMessage = (new BankTransferMail($payload))->onConnection(QueueConnection::MAIL_JOB)->onQueue(MailConstant::ACCOUNT_CREDIT);
            Mail::to($payload['email'])->send($queueMessage);

            // Convert payload to a string for logging or storing
            $payloadString = json_encode($payload);

            // Dispatch the event after sending the email
            event(new EmailSent(
                $payload['user_id'],
                $payload['name'],
                $payload['email'],
                $payload['subject'],
                'Account Credit - Bank Transfer Notification',
                $payloadString,
                null
            ));
        } catch (Exception $e) {
            app(AuthLogger::class)->info('BankTransferMailService: '.$e->getMessage());
            throw new Exception($e->getMessage());
        }
    }
}
