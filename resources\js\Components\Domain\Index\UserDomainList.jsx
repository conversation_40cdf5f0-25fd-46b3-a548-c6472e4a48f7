//* PACKAGES
import React, { useState, useEffect } from 'react';
import { router, usePage } from "@inertiajs/react";

//* ICONS
import { MdOutlineSettings } from "react-icons/md";
import { TbSortAscending2, TbSortDescending2 } from "react-icons/tb";
import { ImCalendar, ImSortAlphaAsc, ImSortAlphaDesc } from "react-icons/im";

//* LAYOUTS
//...

//* COMPONENTS
import _DomainStatus from "@/Constant/_DomainStatus";
import DomainNotFound from "@/Components/Push/DomainNotFound";
import Item from "@/Components/Domain/Item";
import Checkbox from "@/Components/Checkbox";

//* STATE
//...

//* UTILS
//...

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
//...



export default function UserDomainList(
    {
        //! VARIABLES
        params,
        items,

        //! STATES
        selectedIds,

        //! EVENTS
        handleCheckAll,
        handleCheckRow,
        handleRequestAuthcodeBtn
    }
) {
    const SORT_TYPE =
    {
        DOMAIN_ASC: "Domain: Asc",
        DOMAIN_DESC: "Domain: Desc",
        CREATED_ASC: "Created: Asc",
        CREATED_DESC: "Created: Desc",
        EXPIRY_ASC: "Expiration: Asc",
        EXPIRY_DESC: "Expiration: Desc",
        NAMESERVER_ASC: 'Nameserver: Asc',
        NAMESERVER_DESC: 'Nameserver: Desc',
    };

    const DT_FORMAT =
    {
        STRING: 1,
        COUNT: 2,
    }
    
    const { status, name, tld, category, nameserver } = params;
    const activeAndExpiredDomains = items.filter(item => (item.status == 'ACTIVE' || item.status == 'EXPIRED'));
    const hasActiveAndExpired = activeAndExpiredDomains.length > 0;
    const hasItem = items.length > 0;
    const orderby = params.orderby ?? SORT_TYPE.CREATED_DESC;
    const [dateTimeFormat, setDateTimeFormat] = useState(DT_FORMAT.STRING);

    const callSortOrder = (order) => {
        let payload =
        {
            orderby: order,
            status: status,
            name: name,
            tld: tld,
            category: category,
            nameserver: nameserver,
        };

        router.get(route("domain", payload));
    };

    const DomainLabel = () => {
        return (
            <label className="flex items-center pl-2 space-x-2">
                {hasItem && hasActiveAndExpired ? (
                    <Checkbox
                        name="select_all"
                        value="select_all"
                        checked={activeAndExpiredDomains.every((item) => selectedIds.includes(item.id))}
                        handleChange={handleCheckAll}
                    />
                ) : (
                    <div className="opacity-0 pointer-events-none">
                        <Checkbox disabled={true} />
                    </div>
                )}
                <span>Domain</span>
                <button
                    onClick={() =>
                        callSortOrder(
                            orderby === SORT_TYPE.DOMAIN_ASC
                                ? SORT_TYPE.DOMAIN_DESC
                                : SORT_TYPE.DOMAIN_ASC
                        )
                    }
                // disabled={hasItem}
                >
                    {orderby === SORT_TYPE.DOMAIN_ASC ? (
                        <ImSortAlphaAsc />
                    ) : (
                        <ImSortAlphaDesc />
                    )}
                </button>
            </label>
        );
    };

    const ExpirationLabel = () => {
        return (
            <label className="flex items-center space-x-2">
                <span className="">Expiration</span>
                <button
                    onClick={() =>
                        callSortOrder(
                            orderby === SORT_TYPE.EXPIRY_ASC
                                ? SORT_TYPE.EXPIRY_DESC
                                : SORT_TYPE.EXPIRY_ASC
                        )
                    }
                >
                    {orderby === SORT_TYPE.EXPIRY_ASC ? (
                        <TbSortAscending2 />
                    ) : (
                        <TbSortDescending2 />
                    )}
                </button>
                {/* <button onClick={() => toggleDateTimeFormat(dateTimeFormat)}>
                    <ImCalendar />
                </button> */}
            </label>
        );
    };

    const NameserverLabel = () => {
        return (
            <label className="flex items-center space-x-2">
                <span className="">Nameserver</span>
                <button
                    onClick={() =>
                        callSortOrder(
                            orderby === SORT_TYPE.NAMESERVER_ASC
                                ? SORT_TYPE.NAMESERVER_DESC
                                : SORT_TYPE.NAMESERVER_ASC
                        )
                    }
                >
                    {orderby === SORT_TYPE.NAMESERVER_ASC ? (
                        <TbSortAscending2 />
                    ) : (
                        <TbSortDescending2 />
                    )}
                </button>
                {/* <button onClick={() => toggleDateTimeFormat(dateTimeFormat)}>
                    <ImCalendar />
                </button> */}
            </label>
        );
    };

    const CreatedLabel = () => {
        return (
            <label className="flex items-center space-x-2">
                <span className="">Created</span>
                <button
                    onClick={() =>
                        callSortOrder(
                            orderby === SORT_TYPE.CREATED_ASC
                                ? SORT_TYPE.CREATED_DESC
                                : SORT_TYPE.CREATED_ASC
                        )
                    }
                >
                    {orderby === SORT_TYPE.CREATED_ASC ? (
                        <TbSortAscending2 />
                    ) : (
                        <TbSortDescending2 />
                    )}
                </button>
            </label>
        );
    };

    if (!hasItem) return <DomainNotFound />;

    const DomainList = () => {
        return (
            <>
                {
                    items.map(
                        (item, index) =>
                        (
                            <Item
                                item={item}
                                key={"dl-" + index}
                                index={index}
                                isSelected={selectedIds.includes(item.id)}
                                dateTimeFormat={dateTimeFormat}
                                onCheckboxChange={handleCheckRow}
                                onRequestAuthcodeClicked={handleRequestAuthcodeBtn}
                            />
                        )
                    )
                }
            </>
        );
    };

    return (
        <div>
            <table className="min-w-full text-left border-spacing-y-2.5 border-separate">
                <thead className="text-sm bg-gray-50">
                    <tr>
                        <th className="py-3">
                            <DomainLabel />
                        </th>
                        <th>
                            <span><NameserverLabel /></span>
                        </th>
                        <th>
                            <span>Category</span>
                        </th>
                        <th>
                            <span>Restrictions</span>
                        </th>
                        <th>
                            <span>Privacy</span>
                        </th>
                        <th>
                            <span>Auto-Renewal</span>
                        </th>
                        <th>
                            <ExpirationLabel />
                        </th>
                        <th>
                            <CreatedLabel />
                        </th>
                        <th>
                            <span>Status</span>
                        </th>
                        <th>
                            <span className="text-xl">
                                <MdOutlineSettings />
                            </span>
                        </th>
                    </tr>
                </thead>
                <tbody className="text-sm">
                    <DomainList />
                </tbody>
            </table>
        </div>
    );
}
