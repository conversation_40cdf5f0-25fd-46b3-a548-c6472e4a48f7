<?php

namespace App\Mail;

use App\Models\User; 
use App\Models\PaymentSummary;
use App\Modules\CustomLogger\Services\AuthLogger;; 
use App\Mail\Constants\MailConstant;
use App\Modules\MarketPlace\Services\Payments\MarketInvoiceService;
use App\Util\Constant\QueueConnection; 
use App\Util\Helper\CryptHelper;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

use Illuminate\Support\Carbon; 

class BankTransferPaymentPendingInvoiceMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $mailData;

    /**
     * Create a new message instance.
     */
    public function __construct($payload)
    {
        $this->onConnection(QueueConnection::MAIL_JOB);
        $this->onQueue(MailConstant::PAYMENT_INVOICE);

        app(AuthLogger::class)->info('BankTransferPaymentPendingInvoiceMail: Sending');

        $paymentSummary = PaymentSummary::query()
            ->where('id', '=', $payload['paymentSummaryId'])
            ->where('user_id', '=', $payload['userId'])
            ->firstOrFail();

        $bankTransfer = PaymentSummary::query()
            ->select(
                'bank_transfers.id as bankTransferId', 
                'bank_transfers.purpose as bankTransferPurpose',
                'bank_transfers.reference_number as bankTransferReferenceNumber'
            )
            ->where('payment_summaries.id', '=', $payload['paymentSummaryId'])
            ->where('payment_summaries.user_id', '=', $payload['userId'])
            ->leftJoin('payment_services', 'payment_services.id', '=', 'payment_summaries.payment_service_id')
            ->leftJoin('bank_transfers', 'bank_transfers.id', '=', 'payment_services.bank_transfer_id')
            ->firstOrFail();

        $createdAt = Carbon::parse($paymentSummary->created_at);

        $marketPlaceInvoiceOrders = MarketInvoiceService::instance()->getInvoiceData($paymentSummary->payment_market_place_invoice_id, $payload['userId']);

        $orders = [];

        foreach ($marketPlaceInvoiceOrders as $invoiceOrders) 
        {
            array_push(
                $orders,
                [
                    "domain"      => $invoiceOrders->name,
                    "price"       => $invoiceOrders->price,
                    "transferFee" => $invoiceOrders->total_domain_amount,
                    "icannFee"    => $invoiceOrders->total_icann_fee,
                    "subTotal"    => $invoiceOrders->gross_amount,
                ]
            );
        }

        app(AuthLogger::class)->info('BankTransferPaymentPendingInvoiceMail: Orders Count ' . count($orders));

        $data = 
        [
            'bankTransferReferenceNumber' => $bankTransfer->bankTransferReferenceNumber,
            'bankTransferPurpose'         => $bankTransfer->bankTransferPurpose,
            'transactionId'               => CryptHelper::decrypt($paymentSummary->transaction_id),
            'amountDue'                   => $paymentSummary->total_amount,
            'dateIssued'                  => $createdAt->format('F j, Y h:i:s A'),
            'dateDue'                     => $createdAt->copy()->addDays(5)->format('F j, Y h:i:s A'),
            'orders'                      => $orders,
            'paymentSummaryLink'          => route('payment.summary.view', ['id' => $paymentSummary->id])
        ];
    
        $this->mailData = $data;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: "{$this->mailData['bankTransferReferenceNumber']} Bank Transfer Payment Pending Invoice - StrangeDomains",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'Mails.BankTransferPaymentPendingInvoiceMail',
            with: 
            [
                'mailData' => $this->mailData,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
