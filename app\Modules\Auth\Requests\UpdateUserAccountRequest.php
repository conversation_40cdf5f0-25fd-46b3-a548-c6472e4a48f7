<?php

namespace App\Modules\Auth\Requests;

use App\Events\Payment\GenesisAccountBalanceEvent;
use App\Exceptions\FailedRequestException;
use App\Modules\AccountCredit\Services\DepositAccountCreditService;
use App\Modules\Auth\Constants\InviteStatus;
use App\Modules\Auth\Services\RegisterUserService;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Rules\PasswordConfirmed;
use App\Util\Helper\Client\ClientIp;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;
use Illuminate\Validation\Rules\Password;

class UpdateUserAccountRequest extends FormRequest
{
    // protected $redirect = 'bad-request';
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Check invite authorization
        $user = RegisterUserService::authorize($this->all());

        if (!$user) {
            throw new FailedRequestException(400, 'Valid ID is required.', 'Bad Request');
        }

        if ($user->status === InviteStatus::ACCEPTED) {
            throw new FailedRequestException(410, 'This invitation link has already been used.', 'Gone');
        }

        // Log the current time and expires_at for debugging
        \Log::info('Invite expiration check', [
            'now' => now(),
            'expires_at' => $user->expires_at,
            'now_timestamp' => now()->timestamp,
            'expires_timestamp' => Carbon::parse($user->expires_at)->timestamp,
        ]);

        if (now()->timestamp > Carbon::parse($user->expires_at)->endOfDay()->timestamp) {
            throw new FailedRequestException(410, 'Link has expired.', 'Gone');
        }

        $clientIp = ClientIp::getClientIp($this);

        if (strcmp($clientIp, $this->ip) == 0) {
            return true;
        }

        throw new FailedRequestException(403, 'This action is not authorized.', 'Unauthorized');
    }

    public function rules(): array
    {
        return [
            'ip' => ['required', 'string', 'max:16'],
            'first_name' => ['required', 'string', 'min:2', 'max:50', 'regex:/^[a-zA-Z\s]*$/'],
            'last_name' => ['required', 'string', 'min:2', 'max:50', 'regex:/^[a-zA-Z\s]*$/'],
            'email' => [
                'required',
                'string',
                'email:rfc,dns',
                'max:255',
                'regex:/^[0-9a-zA-Z.@_-]*$/',
            ],
            'password' => ['required', 'string', Password::defaults()],
            'password_confirmation' => ['required', 'string', new PasswordConfirmed($this->password)],
            'street' => ['required', 'string', "regex:/^[a-zA-Z0-9\s\-']*$/"],
            'city' => ['required', 'string', 'max:50', "regex:/^[\p{L}\s\-']+$/u"],
            'state_province' => ['required', 'string', 'max:50', "regex:/^[\p{L}\s\-']+$/u"],
            'postal_code' => ['required', 'string', 'min:3', 'max:8', "regex:/^[0-9a-zA-Z\s\-]*$/"],
            'country_code' => ['required', 'string', 'size:2', 'regex:/^[a-zA-Z]*$/'],
        ];
    }

    public function store()
    {
        $user = RegisterUserService::updateUser($this);
        $urlInviteStatus = InviteStatus::ACCEPTED;
        RegisterUserService::setUserIp($this->email, $this->ip);
        RegisterUserService::markURLInvite($urlInviteStatus, $this->user_id);

        // Skip identity verification disable_verification = true
        if ($this->disable_verification) {
            RegisterUserService::SkipIdentityVerification($this->user_id, $this->email, $this->ip);
        }

        event(new GenesisAccountBalanceEvent($this->user_id));
        DepositAccountCreditService::instance()->store([], $this->user_id, PaymentServiceType::SYSTEM_CREDIT);

        Auth::login($user);
    }

    public function messages()
    {
        return [
            'first_name.regex' => 'The first name field must only contain letters.',
            'last_name.regex' => 'The last name field must only contain letters.',
            'state_province.required' => 'The state/province field is required.',
            'password.confirmed' => 'The password confirmation field does not match.',
            'country_code.required' => 'The country field is required.',
        ];
    }
}
