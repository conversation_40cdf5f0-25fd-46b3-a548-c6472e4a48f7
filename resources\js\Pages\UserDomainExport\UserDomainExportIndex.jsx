//* PACKAGES 
import React, {useState, useEffect} from 'react';
import { router, usePage } from "@inertiajs/react";
import { toast } from "react-toastify";

//* ICONS
//...

//* LAYOUTS 
import AccountCenterLayout from "@/Layouts/AccountCenterLayout";

//* COMPONENTS
import _DomainStatus from "@/Constant/_DomainStatus";

//* STATE
import CartCounterState from "@/State/CartCounterState";

//* UTILS
//... 

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
import PartialUserDomainExportTableActions from '@/Pages/UserDomainExport/Partials/PartialUserDomainExportTableActions';
import PartialUserDomainExportTable from '@/Pages/UserDomainExport/Partials/PartialUserDomainExportTable';

export default function UserDomainExportIndex(
    {
        //! VARIABLES
        items,
        cartCount,
        
        //! STATES 
        //...
        
        //! EVENTS
        //..,
    }
)
{
    //! PACKAGE
    const user        = usePage().props.auth.user;
    const params      = route().params;
    const paramStatus = params.status ?? "All";
    
    //! STATES 
    const [selectedItems, setSelectedItems] = useState([]);
    const [currentPage, setCurrentPage]     = useState(1);
    
    const { cartCounter, setCartCounter }                                   = CartCounterState();

  

    const initializeData = () =>
    {
        setCartCounter(cartCount);
    };

    //! USE EFFECTS
    useEffect(() =>
        {
            initializeData();
        },
        []
    );

    useEffect(
        () =>
        {
                const channel = Echo.private(`UserDomainExportFileGeneration.${user.id}`)
                    .listen(
                        '.user-domain-export.file-generation',
                        (e) =>
                        {
                            toast.success(e.message ?? 'Hello World', {autoClose : 5000});
                            
                            router.reload(
                                {
                                    only: ['items']
                                }
                            );
                        }
                    );

                return () => {
                    channel.stopListening('.user-domain-export.file-generation');
                    Echo.leaveChannel(`private-UserDomainExportFileGeneration.${user.id}`);
                };
        },
        []
    );

    useEffect(
        () => 
        {
            if (
                currentPage == 1 &&
                (items.data.length < items.meta.per_page && items.meta.total > items.meta.per_page)
            )
            {
                router.get(
                    route('user-domain-export.index')
                );
            }

            if (items.data.length == 0 && items.meta.total > 0)
            {
                router.get(
                    route('user-domain-export.index')
                );
            }
        }, 
        [
            items 
        ]
    );


    //! FUNCTIONS


    return (
        <AccountCenterLayout
            postRouteName={"user-domain-export"}
        >
            <PartialUserDomainExportTableActions
                paginationMeta={items.meta}
                selectedItems={selectedItems}
                setSelectedItems={setSelectedItems}
            />
            <div
                className="mx-auto container max-w-[1200px] mt-10 flex flex-col space-y-4"
            >
                {/* <div>
                    <h3>Table Rows:</h3>
                    <div
                        className='flex flex-row gap-x-1'
                    >
                        {
                            items.data.length
                        }
                    </div>
                </div>
                <div>
                    <h3>selectedItems (ids):</h3>
                    <div
                        className='flex flex-row gap-x-1'
                    >
                    {selectedItems.map(id => (
                        <span key={id}>{id}</span>
                    ))}
                    </div>
                </div> */}
                <PartialUserDomainExportTable
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    items={items.data}
                    paginationLinks={items.links}
                    paginationMeta={items.meta}
                    selectedItems={selectedItems}
                    setSelectedItems={setSelectedItems}
                />
            </div>
        </AccountCenterLayout>
    );
}
