<?php

namespace App\Mail;

use App\Mail\Constants\Links;
use App\Mail\Constants\MailConstant;
use App\Util\Constant\QueueConnection;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Auth;

class DomainDeletionNotice extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    private $requestCancellation;
    private $firstName;
    public function __construct($requestCancellation)
    {
        $this->onConnection(QueueConnection::MAIL_JOB);
        $this->onQueue(MailConstant::DOMAIN_DELETION);

        $this->firstName = Auth::user()->first_name;
        $this->requestCancellation = $requestCancellation;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: new Address(config('mail.from.address'), config('mail.from.sd_name')),
            subject: 'Domain Deletion Notice',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {

        return new Content(
            markdown: 'Mails.DomainDeletionNotice',
            with: [
                'name' => $this->firstName,
                'domain_name' => $this->requestCancellation['domain'],
                'reason' => $this->requestCancellation['reason'],
                'custom_reason' => $this->requestCancellation['custom_reason'],
                'link' => config('app.url').Links::CONTACT_US_PAGE,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
