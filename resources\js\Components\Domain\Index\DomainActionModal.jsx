//* PACKAGES 
import React, { useState } from "react";

//* ICONS
//... 

//* COMPONENTS
import <PERSON><PERSON> from "@/Components/Modal";
import DangerButton from "@/Components/DangerButton";

//* PARTIALS
//...

//* STATE
//... 

//* UTILS 
//...

//* ENUMS
//... 

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function DomainActionModal(
    {
        //! VARIABLES
        user,

        //! STATES
        hasLockedDomain,
        hasLockedDomainForDelete,
        isModalOpen,
        modalType,

        //! EVENTS
        closeModal,
    }
) {
    //! PACKAGE
    //...

    //! STATES
    //...

    //! VARIABLES 
    //... 

    //! USE EFFECTS
    //... 

    //! FUNCTIONS
    const LockDomainWarning = () => {
        return (
            <p>
                Please ensure that the domain/s are unlocked before requesting
                an authorization code.
            </p>
        );
    };

    const LockDomainDeleteWarning = () => {
        return (
            <p>
                Please ensure that the domain/s are unlocked before attempting
                to delete. Locked domains cannot be deleted for security reasons.
            </p>
        );
    };

    const LockDomainConfirmation = () => {
        return (
            <>
                <p>
                    {`The authentication code will be sent to this email: `}
                    <span className="font-bold underline text-primary">
                        {user.email}
                    </span>
                </p>
                <p>Please ensure that you have access to this email address.</p>
                <p>Do you wish to continue?</p>
            </>
        );
    };

    const ConfirmationAction = () => {
        const isDeleteModal = modalType === 'delete';
        const hasLock = isDeleteModal ? hasLockedDomainForDelete : hasLockedDomain;

        return (
            <div className="flex justify-end space-x-2">
                {!hasLock && (
                    <button
                        onClick={
                            () => {
                                closeModal(true)
                            }
                        }
                        className="bg-primary hover:bg-gray-700 px-4 py-1 rounded-lg text-white"
                    >
                        Continue
                    </button>
                )}
                <DangerButton onClick={() => closeModal(false)}>
                    {`${hasLock ? "Close" : "Cancel"}`}
                </DangerButton>
            </div>
        );
    };

    const HasLockDomainMessage = () => {
        const isDeleteModal = modalType === 'delete';
        const hasLock = isDeleteModal ? hasLockedDomainForDelete : hasLockedDomain;

        if (hasLock) {
            return (
                <>
                    <h1 className="font-bold text-xl">Domain Lock Notice</h1>
                    <div className="mx-6 px-4 border-l-[6px] border-primary">
                        {isDeleteModal ? <LockDomainDeleteWarning /> : <LockDomainWarning />}
                    </div>
                </>
            );
        }

        return (
            <>
                <h1 className="font-bold text-xl">Confirmation</h1>
                <div className="mx-6 px-4 border-l-[6px] border-primary">
                    <LockDomainConfirmation />
                </div>
            </>
        );
    };

    return (
        <>
            <Modal show={isModalOpen} onClose={closeModal}>
                <div className="px-8 py-6 space-y-6">
                    <HasLockDomainMessage />
                    <ConfirmationAction />
                </div>
            </Modal>
        </>

    );
}
