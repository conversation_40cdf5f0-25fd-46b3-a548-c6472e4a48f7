<?php

namespace App\Modules\BankTransfer\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\BankTransfer\Requests\ShowListRequest;
use App\Modules\BankTransfer\Requests\StoreWireTransferRequest;
use App\Modules\BankTransfer\Services\BankTransferService;
use Inertia\Inertia;

class BankTransferController extends Controller
{
    public function index(ShowListRequest $request)
    {
        return Inertia::render('WireTransfer/WireTransferIndex', $request->show());
    }

    public function store(StoreWireTransferRequest $request)
    {
        BankTransferService::instance()->requestStore($request->all()); 

        //! SHOULD NOT REDIRECT SINCE USER WILL BE SENT TO A SUCCESS PAGE VIA CLIENT SIDE
        //return redirect()->route('account.balance.index');
    }
}
