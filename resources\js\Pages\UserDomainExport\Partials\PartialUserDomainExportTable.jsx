//* PACKAGES 
import React from 'react';
import { FaExclamationCircle } from 'react-icons/fa';
import { TbDownload } from 'react-icons/tb';

//* ICONS
//... 

//* COMPONENTS
import AppButtonLinkComponent from '@/Components/App/AppButtonLinkComponent';
import AppButtonLoadingComponent from '@/Components/App/AppButtonLoadingComponent';
import AppInputCheckboxComponent from '@/Components/App/AppInputCheckboxComponent';
import AppNoTableItemsFoundComponent from '@/Components/App/AppNoTableItemsFoundComponent';
import AppTableComponent from '@/Components/App/AppTableComponent';

//* PARTIALS
//... 

//* STATE
//...

//* HOOKS 
//... 

//* UTILS 
import UtilConvertFileSize from '@/Util/UtilConvertFileSize';
import setDefaultDateFormat from '@/Util/setDefaultDateFormat';

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialUserDomainExportTable(
    {
        //! VARIABLES
        items, 
        paginationLinks, 
        paginationMeta, 
        
        //! STATES 
        selectedItems,
        currentPage, 

        //! EVENTS
        setCurrentPage,
        setSelectedItems
    }
) {
    //! VARIABLES 
    const hasItem = items.length > 0;
        
    //! FUNCTIONS
    function handleChangePage(isNext) {
        if (isNext) {
            setCurrentPage(currentPage + 1); 
        } else {
            setCurrentPage(currentPage - 1); 
        }
    }

    function handleCheckAll(e) {
        if (e.currentTarget.checked) {
            const updatedSelectedItems = [...selectedItems];
            items.forEach(item => {
                if (!updatedSelectedItems.includes(item.id)) {
                    updatedSelectedItems.push(item.id);
                }
            });
            setSelectedItems(updatedSelectedItems);
        } else {
            const updatedSelectedItems = selectedItems.filter(
                id => !items.some(item => item.id === id)
            );
            setSelectedItems(updatedSelectedItems);
        }
    }

    function handleCheckRow(e, itemId) {        
        if (e.currentTarget.checked) {
            setSelectedItems([...selectedItems, itemId]);
        } else {
            setSelectedItems(selectedItems.filter(item => item !== itemId));
        }
    }

    //! RENDER VARIABLES
    const tableHeaderItems = (
        <tr>
            <th className='py-3 pl-2'>
                <AppInputCheckboxComponent
                    containerClass={'gap-x-2 capitalize'}
                    id={'select-all'}
                    name={'select-all'}
                    label={'name'}
                    value={true}
                    isChecked={items.every(item => selectedItems.includes(item.id))}
                    isDisabled={false}
                    handleOnChange={handleCheckAll}
                />
            </th>   
            {
                ['no. of domains', 'size', 'last downloaded at', 'generated at', 'actions'].map(
                    (item, index) => (
                        <th key={index} className='capitalize'>{item}</th>
                    )
                )
            }
        </tr>
    );

    const tableBodyItems = items.map((item, index) => (
        <tr key={index}>
            <td className='pl-2'>
                <AppInputCheckboxComponent
                    containerClass={'gap-x-2'}
                    id={`${item.name}${item.id}`}
                    name={`${item.name}${item.id}`}
                    label={item.name}
                    value={true}
                    isChecked={selectedItems.includes(item.id)}
                    isDisabled={false}
                    handleOnChange={(e) => handleCheckRow(e, item.id)}
                />
            </td>
            <td>{item.rows}</td>
            <td>{UtilConvertFileSize(item.size)}</td>
            <td>
                {setDefaultDateFormat(item.lastDownloadedAt) + ' ' + new Date(item.lastDownloadedAt + 'Z').toLocaleTimeString()}
            </td>
            <td>
                {setDefaultDateFormat(item.createdAt) + ' ' + new Date(item.createdAt + 'Z').toLocaleTimeString()}
            </td>
            <td>
                {
                    item.path != null
                        ?
                            <AppButtonLinkComponent
                                link={route('user-domain-export.download', { id: item.id })}
                                label={'download'}
                                icon={<TbDownload className='h-5 w-5' />}
                                className='text-primary bg-white hover:bg-blue-50'
                            />
                        :
                        <AppButtonLoadingComponent />
                }
            </td>
        </tr>
    ));

    return (
        <div className='flex flex-col gap-8'>
            {
                hasItem
                    ?
                        <AppTableComponent
                            tableHeaderItems={tableHeaderItems}
                            tableBodyItems={tableBodyItems}
                            tablePaginationLinks={paginationLinks}
                            tablePaginationMeta={paginationMeta}
                            currentPage={currentPage}
                            handleChangePage={handleChangePage}
                        />
                    :
                        <AppNoTableItemsFoundComponent />
            }
            <div className="flex gap-1 items-center text-slate-400 text-xs">
                <FaExclamationCircle />
                <span>Files are kept for only a maximum of 30 days.</span>
            </div>
        </div>
    );
}
