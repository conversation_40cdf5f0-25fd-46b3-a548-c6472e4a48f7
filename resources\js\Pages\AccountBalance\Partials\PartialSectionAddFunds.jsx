//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'

//* ICONS
//...

//* COMPONENTS
import PrimaryLink from "@/Components/PrimaryLink";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
//... 

//* UTILS
//... 

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialSectionAddFunds(
    {
        balance = 0
    }
)
{
    return (
        <div
            className="flex items-center bg-gray-50 p-10 rounded-sm space-y-1 justify-between bg-[url('/assets/images/wave.svg')] bg-cover bg-center"
        >
            <div className="flex flex-col">
                <label className="text-2xl text-gray-400">
                    Balance
                </label>

                <span className="text-4xl text-gray-700">
                    <span className="text-3xl font-semibold">
                        $ {parseFloat(balance).toFixed(2)}
                    </span>
                </span>
            </div>
            <div className=" inline-flex items-center pr-2">
                <PrimaryLink
                    href={route("account.balance.select-method")}
                    method="get"
                >
                    Add Funds
                </PrimaryLink>
            </div>
        </div>
    );
}
