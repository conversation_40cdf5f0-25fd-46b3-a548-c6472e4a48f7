<?php

namespace App\Modules\Domain\Services\ViewServices;

use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Domain\Requests\ShowListRequest;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DomainBuilder
{
    public Builder $builder;

    public ShowListRequest $request;

    private int $pageLimit = 100;

    public static function instance(): self
    {
        $domainBuilder = new self;

        return $domainBuilder;
    }

    public function getDomainIndex(ShowListRequest $request)
    {
        // DB::enableQueryLog();
        $this->request = $request;

        $this->pageLimit = $request->input('limit', 20);

        $this->query();
        $this->whenHasName();
        $this->whenHasStatus();
        $this->whenHasCategory();
        $this->whenHasTld();
        $this->whenHasOrderby();
        $this->whenHasNameserver();
        $this->selectDomainDetails();
        // dd($this->builder);

        return $this->builder->paginate($this->pageLimit)->withQueryString();
    }

    public function get(int $id)
    {
        $this->query();
        $this->selectDomainDetails();

        return $this->builder->where('domains.id', $id)->get()->first() ?? null;
    }

    private function query()
    {
        $seventyFourDaysAgo = Carbon::now()->subDays(74)->getTimestampMs();

        $this->builder = DB::table('domains')
            ->join('registered_domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('registries', 'registries.id', '=', 'user_contacts.registry_id')
            ->join('extensions', 'extensions.id', '=', 'registered_domains.extension_id')
            ->join('user_categories', 'user_categories.id', '=', 'registered_domains.user_category_id')
            ->whereNull('domains.deleted_at')
            ->where('user_contacts.user_id', Auth::user()->id)
            ->where('registered_domains.status', UserDomainStatus::OWNED)
            ->where('domains.expiry', '>', $seventyFourDaysAgo);
    }

    private function selectDomainDetails()
    {
        $this->builder->select(
            'domains.id as id',
            'domains.id as domain_id',
            'domains.name',
            'domains.status',
            'domains.root',
            'domains.root as tld_id',
            'domains.registrant',
            'domains.year_length',
            'domains.created_at',
            'domains.updated_at',
            'domains.expiry',
            'domains.server_renew_at',
            'domains.client_renew_at',
            'domains.contacts',
            'domains.client_status',
            'domains.hosts',
            'domains.nameservers',
            'domains.auth_code_updated_at',
            'domains.privacy_protection',
            'registered_domains.id as registered_domain_id',
            'registered_domains.user_contact_registrar_id',
            'registered_domains.user_category_id',
            'registered_domains.locked_until',
            'registered_domains.contacts_id',
            'registered_domains.user_category_id',
            'registered_domains.locked_until',
            'registered_domains.contacts_id',
            'registered_domains.extension_id',
            'user_contacts.user_id as user_contact_user_id',
            'extensions.name as extension_name',
            'extensions.name as extension',
            'user_categories.name as user_category_name',
            'registries.name as registry',
            'registries.id as registry_id',
        );
    }

    private function whenHasName()
    {
        $request = $this->request;
        $this->builder->when($request->has('name'), function (Builder $query) use ($request) {
            if (count($request->name) === 1) {
                $query->where('domains.name', 'ilike', $request->name[0] . '%');
            } else {
                $query->whereIn('domains.name', $request->name);
            }
        });
    }

    private function whenHasStatus(): void
    {
        $request = $this->request;

        $this->builder->when($request->has('status'), function (Builder $query) use ($request) {
            $status = preg_replace('/\s+/', '_', strtoupper($request->status));

            if (!in_array($status, DomainStatus::all())) {
                return;
            }

            if ($status === DomainStatus::IN_PROCESS) {
                $query->whereIn('domains.status', [DomainStatus::IN_PROCESS, DomainStatus::PENDING]);
            } else {
                $query->where('domains.status', $status);
            }
        });
    }

    private function whenHasCategory(): void
    {
        $request = $this->request;
        $this->builder->when($request->has('category'), function (Builder $query) use ($request) {
            $categories = explode(',', $request->category);
            if (empty($categories)) {
                return;
            }
            $query->where(function ($q) use ($categories) {
                foreach ($categories as $category) {
                    $q->orWhereLike('user_categories.name', $category . '%');
                }
            });
        });
    }

    private function whenHasTld(): void
    {
        $request = $this->request;
        $this->builder->when($request->has('tld'), function (Builder $query) use ($request) {
            $tld = $request->tld;
            if (!in_array($tld, ['com', 'net', 'org'])) {
                return;
            }
            $query->where('extensions.name', $tld);
        });
    }

    private function whenHasOrderby(): void
    {
        $request = $this->request;
        $this->builder->when($request->has('orderby'), function (Builder $query) use ($request) {
            $orderby = explode(':', $request->orderby);

            if (count($orderby) == 2 && in_array($orderby[1], [' Asc', ' Desc'])) {
                switch ($orderby[0]) {
                    case "Created":
                        $query->orderBy('domains.created_at', trim($orderby[1]));
                        break;
                    case "Expiration":
                        $query->orderBy('domains.expiry', trim($orderby[1]));
                        break;
                    case "Domain":
                        $query->orderBy('domains.name', trim($orderby[1]));
                        break;
                    case "Nameserver":
                        $orderby[1] = trim($orderby[1]);
                        $query->orderByRaw("
                            CASE 
                                WHEN jsonb_typeof(domains.nameservers::jsonb) = 'array' THEN (
                                    Select jsonb_agg(elem order by elem)
                                    from jsonb_array_elements_text(domains.nameservers::jsonb) as elem
                                )::text
                                else NULL
                            END $orderby[1] NULLS LAST
                        ");
                        break;
                    default:
                        $query->orderBy('domains.id', 'desc');
                        break;
                }
            } else {
                $query->orderBy('domains.id', 'desc');
            }
        })
            ->when(!$request->has('orderby'), function (Builder $query) {
                $query->orderBy('domains.id', 'desc');
            });
    }

    private function whenHasNameserver(): void
    {
        $request = $this->request;
        $this->builder->when($request->has('nameserver'), function (Builder $query) use ($request) {
            $nameserver = $request->nameserver;
            $query->where('domains.nameservers', 'ilike', '%' . $nameserver . '%');
        });
    }
}
