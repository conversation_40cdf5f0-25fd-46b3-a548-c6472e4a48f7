<?php

use App\Modules\UserDomainExport\Controllers\UserDomainExportController;

use Illuminate\Support\Facades\Route;

Route::middleware(
    [
        'auth',
        'auth.active',
        'account.setup'
    ]
)
    ->prefix('user-exports')
    ->group(
        function () 
        {
            Route::get('', [UserDomainExportController::class, 'index'])->name('user-domain-export.index');
            Route::post('generate', [UserDomainExportController::class, 'generate'])->name('user-domain-export.generate');
            Route::get('{id}/download', [UserDomainExportController::class, 'download'])->name('user-domain-export.download');
            Route::post('batch-delete', [UserDomainExportController::class, 'batchDelete'])->name('user-domain-export.delete-batch');
            Route::delete('{id}/delete', [UserDomainExportController::class, 'delete'])->name('user-domain-export.delete');
            Route::delete('clear', [UserDomainExportController::class, 'clear'])->name('user-domain-export.clear');
            Route::get('download-batch', [UserDomainExportController::class, 'downloadBatch'])->name('user-domain-export.download-batch');
            Route::get('download-all', [UserDomainExportController::class, 'downloadAll'])->name('user-domain-export.download-all');
        }
    );
