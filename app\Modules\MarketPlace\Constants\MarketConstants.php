<?php

namespace App\Modules\MarketPlace\Constants;

final class MarketConstants
{
    public const JOB_RETRY = 'retry';

    public const JOB_PENDING = 'pending';

    public const JOB_COMPLETE = 'completed';

    public const REFUND_FAILED = 'failed';

    public const REFUND_PENDING = 'pending';

    public const REFUND_COMPLETE = 'completed';

    public const DOMAIN_PENDING_HOLD = 'Pending Hold';

    public const DOMAIN_PENDING_ORDER = 'Pending Order';

    public const DOMAIN_PENDING = 'Pending';

    public const STATUS_REFUNDED = 'Refunded';

    public const DOMAIN_COMPLETED = 'Completed';

    public const STATUS_CANCELLED = 'Cancelled';

    public const DOMAIN_REQUESTED = 'Transfer Requested';

    public const STATUS_FAILED = 'FAILED';

    public const STATUS_COMPLETED = 'COMPLETED';

    public const STATUS_REQUESTED = 'REQUESTED';

    public const STATUS_REJECTED = 'REJECTED';

    public const ERROR_REQUESTED_DOMAIN_LOCKED = 'REQUESTED_DOMAIN_LOCKED';

    public const ERROR_REQUESTED_FAILURE_OTHER = 'REQUESTED_FAILURE_OTHER';

    public const ERROR_COMPLETION_FAILURE_OTHER = 'COMPLETION_FAILURE_OTHER';

    public const ERROR_REQUESTED_INVALID_AUTH_CODE = 'REQUESTED_INVALID_AUTH_CODE';

    // API URLS
    public const MARKET_API = 'https://strangeproxy.com/api/search/'; // https://strangeproxy.com/api/search/keyword?extension=com,net,org

    public const AI_API = 'https://strangeproxy.com/api/domain_suggestions?keywords=';

    public const BASIC_API = 'https://strangeproxy.com/api/';

    public const AFTERNIC_PROD = 'https://purchase.api.afternic.com/v4/domains/aftermarket';

    public const AFTERNIC_DEV = 'https://purchase.api.ote-afternic.com/v4/domains/aftermarket';

    // filters
    public const DEFAULT_FILTERS = 'page=1&limit=10&extension=org,net,com';

    public const MICRO = 1000000;

    public static function getProductStatus()
    {
        return [
            strtolower(self::DOMAIN_PENDING),
            strtolower(self::DOMAIN_REQUESTED),
            strtolower(self::DOMAIN_COMPLETED),
            strtolower(self::STATUS_CANCELLED),
            strtolower(self::DOMAIN_PENDING_HOLD),
            strtolower(self::DOMAIN_PENDING_ORDER),
        ];
    }

    public static function getProductsTab()
    {
        return [
            "all",
            strtolower(self::DOMAIN_PENDING),
            strtolower(self::DOMAIN_REQUESTED),
            strtolower(self::DOMAIN_COMPLETED),
            "canceled"
        ];
    }
}
