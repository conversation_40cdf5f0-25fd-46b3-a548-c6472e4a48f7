//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { MdMoreVert} from "react-icons/md";
import { FaEye, FaRegThumbsUp, FaRegThumbsDown, FaRegHourglass, FaRegQuestionCircle } from 'react-icons/fa';

//* COMPONENTS
import BankTransferStatusComponent from '@/Components/BankTransfer/BankTransferStatusComponent';
import DropDownContainer from "@/Components/DropDownContainer";
import Checkbox from "@/Components/Checkbox";

//* PARTIALS
//... 

//* STATE
//...

//* HOOKS 
//... 

//* UTILS
import useOutsideClick from "@/Util/useOutsideClick";
import setDefaultDateFormat from '@/Util/setDefaultDateFormat';

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialWireTransferTableRowItem(
    {
        item,
        isSelected,
        onCheckboxChange,
    }
)
{
    //! PACKAGE
    const ref = useRef();
    
    //! HOOKS
    useOutsideClick(ref, () => {
        setShow(false);
    });
    
    //! VARIABLES
    //...

    //! STATES
    const [show, setShow] = useState(false);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    //...

    return (
        <tr
            className="hover:bg-gray-100"
        >
            <td
                className='font-semibold text-sm'
            >
                <span>{item.reference_number}</span>
            </td>
            <td>
                <div className="flex items-center pl-2 space-x-2">
                    {/* <Checkbox
                        name="name"
                        value={item.name}
                        checked={isSelected}
                        handleChange={handleCheckboxChange}
                    /> */}
                    <div title={item.account_name}>
                        <span>{item.account_name}</span>
                    </div>
                </div>
            </td>
            <td>
                <span>{item.company}</span>
            </td>
            <td>
                <span>{parseFloat(item.amount).toFixed(2)}</span>
            </td>
            <td>
                <span
                    className='font-semibold text-primary uppercase'
                >
                    {item.purpose}
                </span>
            </td>
            <td>
                <BankTransferStatusComponent
                    item={item}
                />
            </td>
            <td>
                <span>{setDefaultDateFormat(item.created_at) + ' ' + new Date(item.created_at + 'Z').toLocaleTimeString()}</span>
            </td>
            {/* <td>
                <span ref={ref} className="relative">
                    <button
                        className="flex items-center"
                        onClick={() => setShow(!show)}
                    >
                        <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                    </button>
                    <DropDownContainer show={show}>
                        <button
                            className="hover:bg-gray-100 px-5 py-1 flex justify-start"
                            onClick={() => onHandleOnClick(route("category.edit", { ids: [item.id] }), 'get')}
                        >
                            Edit
                        </button>
                        {item.is_default !== true && (
                            <>
                                <button
                                    className="hover:bg-gray-100 px-5 py-1 flex justify-start"
                                    onClick={() => onHandleOnClick(route("category.set.default", { id: [item.id] }), 'patch')}
                                >
                                    Set as Default
                                </button>
                                <button
                                    className="hover:bg-gray-100 px-5 py-1 flex justify-start"
                                    onClick={() => onHandleDelete(item.domain_count, item.id)}
                                >
                                    Delete
                                </button>
                            </>
                        )}
                    </DropDownContainer>
                </span>
            </td> */}
        </tr>
    );
}
